import { CanActivate, ExecutionContext, Injectable, UnauthorizedException } from "@nestjs/common";
import { Reflector } from '@nestjs/core';

@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private reflector: Reflector) { }
  canActivate(context: ExecutionContext): boolean {
    const requiredRoles = this.reflector.get<string[]>('roles', context.getHandler());
    if (!requiredRoles) {
      console.log('No roles required for this endpoint');
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const user = request.user;
    
    console.log('Full user object:', JSON.stringify(user, null, 2));
    console.log('Request path:', request.path);
    console.log('Request method:', request.method);
    
    // Check if user exists and has a role
    if (!user || !user.role) {
      console.log('User has no role assigned');
      throw new UnauthorizedException('User has no role assigned');
    }

    // Debug log to see what's happening
    console.log(`Required roles: ${JSON.stringify(requiredRoles)}, User role: ${user.role}`);
    
    // Check if the user's role is included in the required roles
    const hasRole = requiredRoles.includes(user.role);
    
    console.log('Has required role:', hasRole);
    
    if (!hasRole) {
      console.log(`Authorization failed: User with role ${user.role} cannot access resource requiring ${requiredRoles}`);
      throw new UnauthorizedException(`User with role ${user.role} is not authorized to access this resource`);
    }
    
    return hasRole;
  }
}
