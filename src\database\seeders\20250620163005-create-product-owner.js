const { v4: uuidv4 } = require('uuid');
const bcrypt = require('bcrypt');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const now = new Date();

    // 1. Get ProductOwner Role ID (using raw query)
    const [roles] = await queryInterface.sequelize.query(
      "SELECT TOP 1 id FROM Roles WHERE name = 'ProductOwner'"
    );

    const productOwnerRoleId = roles[0]?.id;

    console.log("productOwnerRoleId", productOwnerRoleId)

    if (!productOwnerRoleId) {
      throw new Error('ProductOwner role not found. Please run the role seeder first.');
    }

    // 2. Create Product Owner
    const hashedPassword = await bcrypt.hash('umavanshi@1101', 10); // Replace with real password or env


    await queryInterface.bulkInsert('ProductOwners', [
      {
        id: uuidv4(),
        firstName: 'Pratik',
        lastName: '<PERSON><PERSON><PERSON>',
        email: '<EMAIL>',
        password: hashedPassword,
        roleId: productOwnerRoleId,
        isActive: true,
        isDeleted: false,
        createdAt: now,
        updatedAt: now,
      }
    ]);
  },

  down: async (queryInterface) => {
    await queryInterface.bulkDelete('Users', { email: '<EMAIL>' });
  }
};
