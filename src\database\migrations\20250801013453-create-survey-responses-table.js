'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.createTable('SurveyResponses', {
        id: {
          type: Sequelize.UUID,
          allowNull: false,
          primaryKey: true,
          defaultValue: Sequelize.UUIDV4,
        },
        user_id: {
          type: Sequelize.UUID,
          allowNull: false,
          references: {
            model: 'Users',
            key: 'id',
          },
          onUpdate: 'CASCADE',
          onDelete: 'NO ACTION',
        },
        company_id: {
          type: Sequelize.UUID,
          allowNull: false,
          references: {
            model: 'Companies',
            key: 'id',
          },
          onUpdate: 'CASCADE',
          onDelete: 'NO ACTION',
        },
        question_id: {
          type: Sequelize.UUID,
          allowNull: false,
          references: {
            model: 'Questions',
            key: 'id',
          },
          onUpdate: 'CASCADE',
          onDelete: 'NO ACTION',
        },
        selected_option_id: {
          type: Sequelize.UUID,
          allowNull: false,
          references: {
            model: 'QuestionOptions',
            key: 'id',
          },
          onUpdate: 'CASCADE',
          onDelete: 'NO ACTION',
        },
        survey_session_id: {
          type: Sequelize.UUID,
          allowNull: false,
        },
        response_date: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.fn('GETDATE'),
        },
        supervisor_id: {
          type: Sequelize.UUID,
          allowNull: true,
          references: {
            model: 'Users',
            key: 'id',
          },
          onUpdate: 'CASCADE',
          onDelete: 'NO ACTION',
        },
        department: {
          type: Sequelize.STRING(100),
          allowNull: true,
        },
        isActive: {
          type: Sequelize.BOOLEAN,
          allowNull: false,
          defaultValue: true,
        },
        createdAt: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.fn('GETDATE'),
        },
        createdBy: {
          type: Sequelize.UUID,
          allowNull: true,
        },
        updatedAt: {
          type: Sequelize.DATE,
          allowNull: false,
          defaultValue: Sequelize.fn('GETDATE'),
        },
        updatedBy: {
          type: Sequelize.UUID,
          allowNull: true,
        },
        isDeleted: {
          type: Sequelize.BOOLEAN,
          allowNull: false,
          defaultValue: false,
        },
        deletedBy: {
          type: Sequelize.UUID,
          allowNull: true,
        },
      });

      // Adding indexes
      await queryInterface.addIndex('SurveyResponses', ['company_id', 'response_date'], {
        name: 'idx_survey_responses_company_date',
      });
      await queryInterface.addIndex('SurveyResponses', ['supervisor_id', 'response_date'], {
        name: 'idx_survey_responses_supervisor_date',
      });
      await queryInterface.addIndex('SurveyResponses', ['user_id', 'response_date'], {
        name: 'idx_survey_responses_user_date',
      });
      await queryInterface.addIndex('SurveyResponses', ['survey_session_id'], {
        name: 'idx_survey_responses_session',
      });
      await queryInterface.addIndex('SurveyResponses', ['company_id', 'question_id', 'response_date'], {
        name: 'idx_survey_responses_analytics',
      });
    } catch (error) {
      console.log("error in survey response migration is", error);
    }

  },

  down: async (queryInterface) => {
    await queryInterface.dropTable('SurveyResponses');
  },
};
