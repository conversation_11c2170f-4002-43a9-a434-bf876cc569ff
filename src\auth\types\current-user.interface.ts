// src/auth/types/current-user.interface.ts

/**
 * Interface defining the structure of the current user object
 * that is available through the @CurrentUser() decorator
 */
export interface CurrentUser {
  /** User ID (same as sub) */
  sub: string;
  
  /** User ID (same as sub) */
  userId: string;
  
  /** User email address */
  email: string;
  
  /** User role name (e.g., 'CompanyAdmin', 'Manager', 'Supervisor', 'Employee') */
  role: string;
  
  /** Company ID the user belongs to */
  companyId: string;
  
  /** Department ID the user belongs to (null for CompanyAdmin or users without departments) */
  department_id: string | null;
  
  /** Whether this is an infinite token (only for CompanyAdmin) */
  isInfiniteToken: boolean;
}

/**
 * Interface defining the JWT payload structure
 */
export interface JwtPayload {
  /** User email address */
  email: string;
  
  /** User ID */
  sub: string;
  
  /** User role name */
  role: string;
  
  /** Company ID */
  companyId: string;
  
  /** Department ID (null for users without departments) */
  department_id: string | null;
  
  /** Token expiration (undefined for infinite tokens) */
  exp?: number;
  
  /** Token issued at */
  iat?: number;
}
