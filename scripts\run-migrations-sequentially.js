#!/usr/bin/env node

/**
 * Sequential Migration Runner for Employee Survey System
 * Runs migrations in proper dependency order
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Employee Survey System - Sequential Migration Runner');
console.log('=====================================================');

// Define migration order based on dependencies
const migrationOrder = [
  // Phase 1: Core Foundation Tables (no dependencies)
  {
    phase: 'Foundation',
    migrations: [
      'create-roles-table.js',
      'create-languages-table.js',
      'create-answer-categories-table.js'
    ]
  },
  
  // Phase 2: User Management (depends on roles)
  {
    phase: 'User Management',
    migrations: [
      'create-product-owner-table.js',
      'create-users-table.js'
    ]
  },
  
  // Phase 3: Company Structure (depends on product owners and users)
  {
    phase: 'Company Structure',
    migrations: [
      'create-company-table.js',
      'create-departments-table.js'
    ]
  },
  
  // Phase 4: Question System (depends on languages and categories)
  {
    phase: 'Question System',
    migrations: [
      'create-questions-table.js',
      'create-question-options-table.js',
      'create-question-translations-table.js',
      'create-question-option-translations-table.js'
    ]
  },
  
  // Phase 5: Company-Question Mapping (depends on companies and questions)
  {
    phase: 'Company-Question Mapping',
    migrations: [
      'create-company-question-map-table.js'
    ]
  },
  
  // Phase 6: Survey Responses (depends on users, companies, questions)
  {
    phase: 'Survey System',
    migrations: [
      'create-survey-responses-table.js',
      'create-reports-table.js'
    ]
  },
  
  // Phase 7: Schema Modifications (depends on existing tables)
  {
    phase: 'Schema Updates',
    migrations: [
      'add-category-id-to-question-options.js',
      'add-department-id-to-users.js',
      'add-refresh-token-to-product-owners.js',
      'add-temporary-password-field.js'
    ]
  },
  
  // Phase 8: Data Migration (depends on all tables being ready)
  {
    phase: 'Data Migration',
    migrations: [
      'migrate-existing-texts-to-translations.js'
    ]
  }
];

// Get all available migration files
function getAvailableMigrations() {
  const migrationsDir = path.join(process.cwd(), 'src', 'database', 'migrations');
  
  if (!fs.existsSync(migrationsDir)) {
    console.error('❌ Migrations directory not found:', migrationsDir);
    process.exit(1);
  }
  
  return fs.readdirSync(migrationsDir)
    .filter(file => file.endsWith('.js'))
    .sort(); // Natural sort by timestamp
}

// Check migration status
async function checkMigrationStatus() {
  try {
    console.log('📋 Checking current migration status...');
    const result = execSync('npx sequelize-cli db:migrate:status', {
      encoding: 'utf8',
      cwd: process.cwd()
    });
    console.log(result);
    return true;
  } catch (error) {
    console.log('⚠️  Could not check migration status (database might not be initialized)');
    return false;
  }
}

// Run a single migration
async function runMigration(migrationFile) {
  try {
    console.log(`⚡ Running: ${migrationFile}`);
    
    execSync(`npx sequelize-cli db:migrate --to ${migrationFile}`, {
      stdio: 'inherit',
      cwd: process.cwd()
    });
    
    console.log(`✅ Completed: ${migrationFile}`);
    return true;
  } catch (error) {
    console.error(`❌ Failed: ${migrationFile}`);
    console.error('Error:', error.message);
    return false;
  }
}

// Main execution function
async function runMigrationsSequentially() {
  const availableMigrations = getAvailableMigrations();
  console.log(`📁 Found ${availableMigrations.length} migration files`);
  
  // Check current status
  // await checkMigrationStatus();
  
  let totalMigrations = 0;
  let successfulMigrations = 0;
  let skippedMigrations = 0;
  
  // Process each phase
  for (const phase of migrationOrder) {
    console.log(`\n🔄 Phase: ${phase.phase}`);
    console.log('─'.repeat(50));
    
    for (const migrationName of phase.migrations) {
      totalMigrations++;
      
      // Find the actual migration file (it might have a timestamp prefix)
      const actualMigrationFile = availableMigrations.find(file => 
        file.includes(migrationName) || file === migrationName
      );
      
      if (!actualMigrationFile) {
        console.log(`⚠️  Migration not found: ${migrationName} (skipping)`);
        skippedMigrations++;
        continue;
      }
      
      const success = await runMigration(actualMigrationFile);
      if (success) {
        successfulMigrations++;
      } else {
        console.error(`\n❌ Migration failed: ${actualMigrationFile}`);
        console.error('🛑 Stopping migration process due to failure');
        process.exit(1);
      }
    }
  }
  
  // Summary
  console.log('\n🎉 Migration Summary');
  console.log('===================');
  console.log(`✅ Successful: ${successfulMigrations}`);
  console.log(`⚠️  Skipped: ${skippedMigrations}`);
  console.log(`📊 Total: ${totalMigrations}`);
  
  if (successfulMigrations === totalMigrations - skippedMigrations) {
    console.log('\n🎯 All available migrations completed successfully!');
    console.log('\n📋 Next Steps:');
    console.log('1. Run seeders: npm run seed');
    console.log('2. Verify database: npx sequelize-cli db:migrate:status');
    console.log('3. Start your application: npm start');
  }
}

// Error handling
process.on('unhandledRejection', (error) => {
  console.error('❌ Unhandled error:', error);
  process.exit(1);
});

// Run the migrations
runMigrationsSequentially().catch(error => {
  console.error('❌ Migration process failed:', error);
  process.exit(1);
});
