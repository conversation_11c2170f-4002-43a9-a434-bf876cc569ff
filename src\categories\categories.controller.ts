import { 
  Controller, 
  Get, 
  Post, 
  Put, 
  Delete,
  Body, 
  Param, 
  UseGuards,
  ParseUUIDPipe,
  BadRequestException,
  NotFoundException
} from '@nestjs/common';

import { CreateCategoryDto, UpdateCategoryDto } from './dto/category.dto';
import { JwtAuthGuard } from 'src/auth/guards/jwt-auth-guards';
import { CategoriesService } from './categories.service';

@Controller('api/categories')
@UseGuards(JwtAuthGuard)
export class CategoriesController {
  constructor(private readonly categoriesService: CategoriesService) {}

  /**
   * Get all active answer categories
   * GET /api/categories
   */
  @Get()
  async getAllCategories() {
    try {
      return await this.categoriesService.getAllActiveCategories();
    } catch (error) {
      throw new BadRequestException(`Failed to get categories: ${error.message}`);
    }
  }

  /**
   * Get category by ID
   * GET /api/categories/:id
   */
  @Get(':id')
  async getCategoryById(@Param('id', ParseUUIDPipe) id: string) {
    try {
      const category = await this.categoriesService.getCategoryById(id);
      if (!category) {
        throw new NotFoundException(`Category with ID ${id} not found`);
      }
      return category;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(`Failed to get category: ${error.message}`);
    }
  }

  /**
   * Get category by name
   * GET /api/categories/name/:name
   */
  @Get('name/:name')
  async getCategoryByName(@Param('name') name: string) {
    try {
      const category = await this.categoriesService.getCategoryByName(name);
      if (!category) {
        throw new NotFoundException(`Category with name ${name} not found`);
      }
      return category;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(`Failed to get category: ${error.message}`);
    }
  }

  /**
   * Create new answer category
   * POST /api/categories
   */
  @Post()
  async createCategory(@Body() createCategoryDto: CreateCategoryDto) {
    try {
      return await this.categoriesService.createCategory(createCategoryDto);
    } catch (error) {
      throw new BadRequestException(`Failed to create category: ${error.message}`);
    }
  }

  /**
   * Update answer category
   * PUT /api/categories/:id
   */
  @Put(':id')
  async updateCategory(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateCategoryDto: UpdateCategoryDto
  ) {
    try {
      const updatedCategory = await this.categoriesService.updateCategory(id, updateCategoryDto);
      if (!updatedCategory) {
        throw new NotFoundException(`Category with ID ${id} not found`);
      }
      return updatedCategory;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(`Failed to update category: ${error.message}`);
    }
  }

  /**
   * Soft delete answer category
   * DELETE /api/categories/:id
   */
  @Delete(':id')
  async deleteCategory(@Param('id', ParseUUIDPipe) id: string) {
    try {
      const result = await this.categoriesService.deleteCategory(id);
      if (!result) {
        throw new NotFoundException(`Category with ID ${id} not found`);
      }
      return { message: 'Category deleted successfully' };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(`Failed to delete category: ${error.message}`);
    }
  }

  /**
   * Get categories with question option counts
   * GET /api/categories/usage-stats
   */
  @Get('usage-stats')
  async getCategoryUsageStats() {
    try {
      return await this.categoriesService.getCategoryUsageStats();
    } catch (error) {
      throw new BadRequestException(`Failed to get category usage stats: ${error.message}`);
    }
  }

  /**
   * Reorder categories
   * PUT /api/categories/reorder
   */
  @Put('reorder')
  async reorderCategories(@Body() reorderData: { categoryId: string; newOrder: number }[]) {
    try {
      return await this.categoriesService.reorderCategories(reorderData);
    } catch (error) {
      throw new BadRequestException(`Failed to reorder categories: ${error.message}`);
    }
  }
}
