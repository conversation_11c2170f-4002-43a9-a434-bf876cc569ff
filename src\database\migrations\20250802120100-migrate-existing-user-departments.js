'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Migrate existing user-department relationships from Users table to UserDepartments junction table
    const [users] = await queryInterface.sequelize.query(`
      SELECT id, department_id, createdBy, createdAt, updatedAt 
      FROM Users 
      WHERE department_id IS NOT NULL AND isDeleted = 0
    `);

    if (users.length > 0) {
      const userDepartmentRecords = users.map(user => ({
        id: Sequelize.fn('NEWID'),
        user_id: user.id,
        department_id: user.department_id,
        isActive: true,
        createdAt: user.createdAt || new Date(),
        createdBy: user.createdBy,
        updatedAt: user.updatedAt || new Date(),
        updatedBy: user.createdBy,
        isDeleted: false,
        deletedBy: null,
      }));

      await queryInterface.bulkInsert('UserDepartments', userDepartmentRecords);
      
      console.log(`Migrated ${users.length} user-department relationships to UserDepartments table`);
    } else {
      console.log('No existing user-department relationships found to migrate');
    }
  },

  async down(queryInterface, Sequelize) {
    // Remove all records from UserDepartments table
    await queryInterface.bulkDelete('UserDepartments', null, {});
    console.log('Removed all records from UserDepartments table');
  }
};
