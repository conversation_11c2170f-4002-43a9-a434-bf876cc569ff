import { Injectable, CanActivate, ExecutionContext, ForbiddenException, Logger, SetMetadata } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Request } from 'express';

// Decorator to specify allowed domains
export const AllowedDomains = (domains: string[]) => SetMetadata('allowedDomains', domains);

@Injectable()
export class DomainRestrictionGuard implements CanActivate {
  private readonly logger = new Logger(DomainRestrictionGuard.name);

  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest<Request>();
    
    // Get allowed domains from decorator
    const allowedDomains = this.reflector.get<string[]>('allowedDomains', context.getHandler());
    
    if (!allowedDomains || allowedDomains.length === 0) {
      this.logger.warn('No allowed domains specified for this endpoint');
      return true; // If no domains specified, allow all
    }

    // Get the origin or referer from the request
    const origin = request.headers.origin;
    const referer = request.headers.referer;
    const host = request.headers.host;

    this.logger.debug(`Request details:`, {
      origin,
      referer,
      host,
      userAgent: request.headers['user-agent'],
      ip: this.getClientIp(request)
    });

    // Extract domain from origin, referer, or host
    const requestDomain = this.extractDomain(origin || referer || `http://${host}`);
    
    if (!requestDomain) {
      this.logger.warn('Could not determine request domain', {
        origin,
        referer,
        host
      });
      throw new ForbiddenException('Request domain could not be determined');
    }

    // Check if the request domain is in the allowed list
    const isAllowed = allowedDomains.some(allowedDomain => {
      // Support wildcard subdomains (e.g., *.example.com)
      if (allowedDomain.startsWith('*.')) {
        const baseDomain = allowedDomain.substring(2);
        return requestDomain.endsWith(baseDomain);
      }
      
      // Exact domain match
      return requestDomain === allowedDomain;
    });

    if (!isAllowed) {
      this.logger.warn(`Domain restriction violation`, {
        requestDomain,
        allowedDomains,
        ip: this.getClientIp(request),
        userAgent: request.headers['user-agent']
      });
      
      throw new ForbiddenException(`Access denied. Domain '${requestDomain}' is not authorized to access this resource.`);
    }

    this.logger.debug(`Domain access granted for: ${requestDomain}`);
    
    // Store domain info in request for later use
    (request as any).domainInfo = {
      domain: requestDomain,
      origin,
      referer,
      ip: this.getClientIp(request),
      userAgent: request.headers['user-agent']
    };

    return true;
  }

  private extractDomain(url: string): string | null {
    try {
      if (!url) return null;
      
      // Handle cases where URL doesn't have protocol
      if (!url.startsWith('http://') && !url.startsWith('https://')) {
        url = `https://${url}`;
      }
      
      const urlObj = new URL(url);
      return urlObj.hostname;
    } catch (error) {
      this.logger.error('Error extracting domain from URL:', url, error);
      return null;
    }
  }

  private getClientIp(request: Request): string {
    return (
      request.headers['x-forwarded-for'] as string ||
      request.headers['x-real-ip'] as string ||
      request.connection?.remoteAddress ||
      request.socket?.remoteAddress ||
      'unknown'
    );
  }
}

// Environment-based domain configuration
export const getDomainConfig = () => {
  const environment = process.env.NODE_ENV || 'development';
  
  switch (environment) {
    case 'production':
      return {
        allowedDomains: [
          'yourdomain.com',
          'www.yourdomain.com',
          '*.yourdomain.com'
        ]
      };
    case 'staging':
      return {
        allowedDomains: [
          'staging.yourdomain.com',
          'test.yourdomain.com',
          'yourdomain.com'
        ]
      };
    default: // development
      return {
        allowedDomains: [
          'localhost',
          '127.0.0.1',
          'localhost:3000',
          'localhost:3001',
          'localhost:4200',
          '*.localhost'
        ]
      };
  }
};
