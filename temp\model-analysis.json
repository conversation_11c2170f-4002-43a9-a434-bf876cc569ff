{"timestamp": "2025-08-01T01:26:15.872Z", "totalModels": 14, "orderedModels": [{"order": 1, "fileName": "answer-category.model.ts", "tableName": "AnswerCategories", "dependencies": [], "schema": {"id": {"type": "UUID", "primaryKey": true, "defaultValue": "UUIDV4"}, "name": {"type": "STRING(100)", "allowNull": false, "unique": true}, "description": {"type": "TEXT", "allowNull": true}, "color": {"type": "STRING(7)", "allowNull": true}, "isActive": {"type": "BOOLEAN", "defaultValue": true}, "createdAt": {"type": "DATE", "allowNull": false}, "updatedAt": {"type": "DATE", "allowNull": false}}}, {"order": 2, "fileName": "company.model.ts", "tableName": "Companies", "dependencies": ["product-owner-model.ts"], "schema": {"id": {"type": "UUID", "primaryKey": true, "defaultValue": "UUIDV4"}, "name": {"type": "STRING(150)", "allowNull": false}, "product_owner_id": {"type": "UUID", "allowNull": false, "foreignKey": {"table": "ProductOwners", "key": "id"}}, "contact_person_firstName": {"type": "STRING(150)", "allowNull": false}, "contact_person_lastName": {"type": "STRING(150)", "allowNull": false}, "contact_person_email": {"type": "STRING(150)", "allowNull": false}, "contact_person_phone": {"type": "STRING(20)", "allowNull": true}, "address_line": {"type": "STRING(255)", "allowNull": false}, "zipcode": {"type": "STRING(20)", "allowNull": false}, "country": {"type": "STRING(100)", "allowNull": false}, "state": {"type": "STRING(100)", "allowNull": false}, "city": {"type": "STRING(100)", "allowNull": false}, "isActive": {"type": "BOOLEAN", "defaultValue": true}, "isDeleted": {"type": "BOOLEAN", "defaultValue": false}, "createdAt": {"type": "DATE", "allowNull": false}, "updatedAt": {"type": "DATE", "allowNull": false}, "createdBy": {"type": "UUID", "allowNull": true}, "updatedBy": {"type": "UUID", "allowNull": true}, "deletedBy": {"type": "UUID", "allowNull": true}}}, {"order": 3, "fileName": "question.model.ts", "tableName": "Questions", "dependencies": [], "schema": {}}, {"order": 4, "fileName": "company-question-map.model.ts", "tableName": "CompanyQuestionMaps", "dependencies": ["company.model.ts", "question.model.ts"], "schema": {}}, {"order": 5, "fileName": "contact-submission.model.ts", "tableName": "ContactSubmissions", "dependencies": [], "schema": {}}, {"order": 6, "fileName": "department.model.ts", "tableName": "Departments", "dependencies": ["company.model.ts"], "schema": {}}, {"order": 7, "fileName": "language.model.ts", "tableName": "Languages", "dependencies": [], "schema": {"id": {"type": "UUID", "primaryKey": true, "defaultValue": "UUIDV4"}, "name": {"type": "STRING(100)", "allowNull": false}, "code": {"type": "STRING(10)", "allowNull": false, "unique": true}, "isActive": {"type": "BOOLEAN", "defaultValue": true}, "isDefault": {"type": "BOOLEAN", "defaultValue": false}, "createdAt": {"type": "DATE", "allowNull": false}, "updatedAt": {"type": "DATE", "allowNull": false}}}, {"order": 8, "fileName": "question-option.model.ts", "tableName": "QuestionOptions", "dependencies": ["question.model.ts", "answer-category.model.ts"], "schema": {}}, {"order": 9, "fileName": "question-option-translation.model.ts", "tableName": "QuestionOptionTranslations", "dependencies": ["question-option.model.ts", "language.model.ts"], "schema": {}}, {"order": 10, "fileName": "question-translation.model.ts", "tableName": "QuestionTranslations", "dependencies": ["question.model.ts", "language.model.ts"], "schema": {}}, {"order": 11, "fileName": "role.model.ts", "tableName": "Roles", "dependencies": [], "schema": {"id": {"type": "UUID", "primaryKey": true, "defaultValue": "UUIDV4"}, "name": {"type": "STRING", "allowNull": false}, "description": {"type": "STRING", "allowNull": true}, "isActive": {"type": "BOOLEAN", "defaultValue": true}, "isDeleted": {"type": "BOOLEAN", "defaultValue": false}, "createdAt": {"type": "DATE", "allowNull": false}, "updatedAt": {"type": "DATE", "allowNull": false}}}, {"order": 12, "fileName": "user.model.ts", "tableName": "Users", "dependencies": ["role.model.ts", "company.model.ts", "department.model.ts"], "schema": {}}, {"order": 13, "fileName": "report.model.ts", "tableName": "Reports", "dependencies": ["company.model.ts", "user.model.ts"], "schema": {}}, {"order": 14, "fileName": "survey-response.model.ts", "tableName": "SurveyResponses", "dependencies": ["user.model.ts", "company.model.ts", "question.model.ts", "question-option.model.ts"], "schema": {}}], "dependencies": {"role.model.ts": [], "language.model.ts": [], "answer-category.model.ts": [], "product-owner-model.ts": ["role.model.ts"], "company.model.ts": ["product-owner-model.ts"], "department.model.ts": ["company.model.ts"], "user.model.ts": ["role.model.ts", "company.model.ts", "department.model.ts"], "question.model.ts": [], "question-option.model.ts": ["question.model.ts", "answer-category.model.ts"], "question-translation.model.ts": ["question.model.ts", "language.model.ts"], "question-option-translation.model.ts": ["question-option.model.ts", "language.model.ts"], "company-question-map.model.ts": ["company.model.ts", "question.model.ts"], "survey-response.model.ts": ["user.model.ts", "company.model.ts", "question.model.ts", "question-option.model.ts"], "report.model.ts": ["company.model.ts", "user.model.ts"], "contact-submission.model.ts": []}}