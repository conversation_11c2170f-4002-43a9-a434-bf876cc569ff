import { IsString, IsNotEmpty, IsArray, ValidateNested, IsOptional, ArrayMinSize } from 'class-validator';
import { Type } from 'class-transformer';
import { CreateQuestionOptionDto } from './create-question-option.dto';
import { IsTranslationOrString, TranslationObject } from '../../common/validators/translation.validator';

export class CreateQuestionDto {
  @IsTranslationOrString({
    message: 'question_text must be either a non-empty string or a translation object with at least English (en) translation'
  })
  question_text: string | TranslationObject;

  @IsArray()
  @ValidateNested({ each: true })
  @ArrayMinSize(1)
  @Type(() => CreateQuestionOptionDto)
  options: CreateQuestionOptionDto[];

  @IsOptional()
  @IsString()
  createdBy?: string;
}