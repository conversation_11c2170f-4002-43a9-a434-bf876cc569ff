import { IsString, <PERSON>NotEmpty, IsOptional, IsUUID, IsEnum, IsDateString, IsObject, IsBoolean, IsInt, Min } from 'class-validator';
import { ReportType, ReportStatus, ReportFormat } from '../../database/models/report.model';

export class CreateReportDto {
  @IsString()
  @IsNotEmpty()
  name: string;

  @IsEnum(ReportType)
  type: ReportType;

  @IsEnum(ReportFormat)
  @IsOptional()
  format?: ReportFormat = ReportFormat.JSON;

  @IsUUID('4')
  company_id: string;

  @IsDateString()
  start_date: string;

  @IsDateString()
  end_date: string;

  @IsObject()
  @IsOptional()
  parameters?: Record<string, any>;

  @IsOptional()
  @IsUUID('4')
  requested_by?: string;

  @IsOptional()
  @IsUUID('4')
  createdBy?: string;
}

export class UpdateReportDto {
  @IsString()
  @IsOptional()
  name?: string;

  @IsEnum(ReportStatus)
  @IsOptional()
  status?: ReportStatus;

  @IsObject()
  @IsOptional()
  data?: Record<string, any>;

  @IsString()
  @IsOptional()
  file_path?: string;

  @IsInt()
  @Min(0)
  @IsOptional()
  file_size?: number;

  @IsDateString()
  @IsOptional()
  processing_started_at?: string;

  @IsDateString()
  @IsOptional()
  processing_completed_at?: string;

  @IsString()
  @IsOptional()
  error_message?: string;

  @IsDateString()
  @IsOptional()
  expires_at?: string;

  @IsBoolean()
  @IsOptional()
  isActive?: boolean;

  @IsOptional()
  @IsUUID('4')
  updatedBy?: string;
}

export class ReportQueryDto {
  @IsUUID('4')
  @IsOptional()
  company_id?: string;

  @IsEnum(ReportType)
  @IsOptional()
  type?: ReportType;

  @IsEnum(ReportStatus)
  @IsOptional()
  status?: ReportStatus;

  @IsEnum(ReportFormat)
  @IsOptional()
  format?: ReportFormat;

  @IsDateString()
  @IsOptional()
  start_date?: string;

  @IsDateString()
  @IsOptional()
  end_date?: string;

  @IsUUID('4')
  @IsOptional()
  requested_by?: string;

  @IsInt()
  @Min(1)
  @IsOptional()
  limit?: number = 10;

  @IsInt()
  @Min(0)
  @IsOptional()
  offset?: number = 0;
}

export class ReportResponseDto {
  id: string;
  name: string;
  type: ReportType;
  status: ReportStatus;
  format: ReportFormat;
  company_id: string;
  requested_by: string;
  start_date: Date;
  end_date: Date;
  parameters?: Record<string, any>;
  data?: Record<string, any>;
  file_path?: string;
  file_size?: number;
  processing_started_at?: Date;
  processing_completed_at?: Date;
  error_message?: string;
  expires_at?: Date;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  duration?: number;
  isExpired?: boolean;
  isProcessing?: boolean;
  isCompleted?: boolean;
  isFailed?: boolean;
}

export class GenerateReportDto {
  @IsEnum(ReportType)
  type: ReportType;

  @IsUUID('4')
  company_id: string;

  @IsDateString()
  start_date: string;

  @IsDateString()
  end_date: string;

  @IsEnum(ReportFormat)
  @IsOptional()
  format?: ReportFormat = ReportFormat.JSON;

  @IsObject()
  @IsOptional()
  parameters?: Record<string, any>;

  @IsString()
  @IsOptional()
  name?: string;
}
