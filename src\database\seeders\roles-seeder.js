'use strict';

const { v4: uuidv4 } = require('uuid');

/** @param {import('sequelize').QueryInterface} queryInterface */
module.exports = {
  up: async (queryInterface) => {
    try {

      const now = new Date();

      await queryInterface.bulkInsert('Roles', [
        {
          id: uuidv4(),
          name: 'ProductOwner',
          description: 'Product owner of the entire platform',
          isActive: true,
          isDeleted: false,
          createdAt: now,
          updatedAt: now,
        },
        {
          id: uuidv4(),
          name: 'CompanyAdmin',
          description: 'Admin user for the company',
          isActive: true,
          isDeleted: false,
          createdAt: now,
          updatedAt: now,
        },
        {
          id: uuidv4(),
          name: 'Supervisor',
          description: 'Supervisor role for employee oversight',
          isActive: true,
          isDeleted: false,
          createdAt: now,
          updatedAt: now,
        },
        {
          id: uuidv4(),
          name: 'Employee',
          description: 'Standard employee user',
          isActive: true,
          isDeleted: false,
          createdAt: now,
          updatedAt: now,
        },
        {
          id: uuidv4(),
          name: 'CompanyAdministrator',
          description: 'Standard Company Administrator',
          isActive: true,
          isDeleted: false,
          createdAt: now,
          updatedAt: now,
        },
            {
          id: uuidv4(),
          name: 'CompanyManager',
          description: 'Standard Company Manager',
          isActive: true,
          isDeleted: false,
          createdAt: now,
          updatedAt: now,
        },
      ]);

      console.log('Roles seeded!');
    } catch (err) {
      console.error('Seeding failed:', err);
    }
  },

  down: async (queryInterface) => {
    await queryInterface.bulkDelete('Roles', null, {});
  },
};
