import { Modu<PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { UsersController } from './users.controller';
import { UsersService } from './users.service';
import { User } from '../database/models/user.model';
import { Role } from '../database/models/role.model';
import { Company } from '../database/models/company.model';
import { Department } from '../database/models/department.model';
import { SurveyResponse } from '../database/models/survey-response.model';
import { UserDepartment } from '../database/models/user-department.model';
import { EmailService } from '../common/services/email.service';
import { SurveyModule } from '../survey/survey.module';

@Module({
  imports: [
    SequelizeModule.forFeature([User, Role, Company, Department, SurveyResponse, UserDepartment]),
    SurveyModule,
  ],
  controllers: [UsersController],
  providers: [UsersService, EmailService],
  exports: [UsersService, EmailService],
})
export class UsersModule {}
