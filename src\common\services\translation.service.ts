import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Sequelize } from 'sequelize-typescript';
import { CreationAttributes } from 'sequelize';
import { Language } from '../../database/models/language.model';
import { QuestionTranslation } from '../../database/models/question-translation.model';
import { QuestionOptionTranslation } from '../../database/models/question-option-translation.model';

export interface TranslationData {
  [languageCode: string]: string;
}

@Injectable()
export class TranslationService {
  private readonly DEFAULT_LANGUAGE = 'en';

  constructor(
    @InjectModel(Language)
    private readonly languageModel: typeof Language,
    @InjectModel(QuestionTranslation)
    private readonly questionTranslationModel: typeof QuestionTranslation,
    @InjectModel(QuestionOptionTranslation)
    private readonly questionOptionTranslationModel: typeof QuestionOptionTranslation,
    private readonly sequelize: Sequelize,
  ) {}

  /**
   * Get all active languages
   */
  async getActiveLanguages(): Promise<Language[]> {
    return this.languageModel.findAll({
      where: {
        isActive: true,
        isDeleted: false
      },
      order: [['isDefault', 'DESC'], ['name', 'ASC']]
    });
  }

  /**
   * Get language by code
   */
  async getLanguageByCode(code: string): Promise<Language | null> {
    return this.languageModel.findOne({
      where: {
        code: code.toLowerCase(),
        isActive: true,
        isDeleted: false
      }
    });
  }

  /**
   * Get default language
   */
  async getDefaultLanguage(): Promise<Language | null> {
    return this.languageModel.findOne({
      where: {
        isDefault: true,
        isActive: true,
        isDeleted: false
      }
    });
  }

  /**
   * Create question translations
   */
  async createQuestionTranslations(
    questionId: string,
    translations: TranslationData,
    createdBy?: string
  ): Promise<QuestionTranslation[]> {
    const languages = await this.getActiveLanguages();
    const languageMap = new Map(languages.map(lang => [lang.code, lang.id]));
    
    const translationRecords: CreationAttributes<QuestionTranslation>[] = [];

    for (const [languageCode, text] of Object.entries(translations)) {
      const languageId = languageMap.get(languageCode.toLowerCase());
      if (languageId && text.trim()) {
        translationRecords.push({
          question_id: questionId,
          language_id: languageId,
          question_text: text.trim(),
          createdBy: createdBy || ''
        } as CreationAttributes<QuestionTranslation>);
      }
    }

    if (translationRecords.length > 0) {
      return this.questionTranslationModel.bulkCreate(translationRecords);
    }

    return [];
  }

  /**
   * Create question option translations
   */
  async createQuestionOptionTranslations(
    questionOptionId: string,
    translations: TranslationData,
    createdBy?: string
  ): Promise<QuestionOptionTranslation[]> {
    const languages = await this.getActiveLanguages();
    const languageMap = new Map(languages.map(lang => [lang.code, lang.id]));
    
    const translationRecords: CreationAttributes<QuestionOptionTranslation>[] = [];

    for (const [languageCode, text] of Object.entries(translations)) {
      const languageId = languageMap.get(languageCode.toLowerCase());
      if (languageId && text.trim()) {
        translationRecords.push({
          question_option_id: questionOptionId,
          language_id: languageId,
          option_text: text.trim(),
          createdBy: createdBy || ''
        } as CreationAttributes<QuestionOptionTranslation>);
      }
    }

    if (translationRecords.length > 0) {
      return this.questionOptionTranslationModel.bulkCreate(translationRecords);
    }

    return [];
  }

  /**
   * Get question translation for specific language
   */
  async getQuestionTranslation(questionId: string, languageCode: string): Promise<string | null> {
    const language = await this.getLanguageByCode(languageCode);
    if (!language) {
      return null;
    }

    const translation = await this.questionTranslationModel.findOne({
      where: {
        question_id: questionId,
        language_id: language.id,
        isActive: true,
        isDeleted: false
      }
    });

    return translation ? translation.question_text : null;
  }

  /**
   * Get question option translation for specific language
   */
  async getQuestionOptionTranslation(questionOptionId: string, languageCode: string): Promise<string | null> {
    const language = await this.getLanguageByCode(languageCode);
    if (!language) {
      return null;
    }

    const translation = await this.questionOptionTranslationModel.findOne({
      where: {
        question_option_id: questionOptionId,
        language_id: language.id,
        isActive: true,
        isDeleted: false
      }
    });

    return translation ? translation.option_text : null;
  }

  /**
   * Get question text with fallback logic
   */
  async getLocalizedQuestionText(questionId: string, languageCode: string, fallbackText: string): Promise<string> {
    // Try to get translation for requested language
    const translation = await this.getQuestionTranslation(questionId, languageCode);
    if (translation) {
      return translation;
    }

    // Fallback to default language if not the same as requested
    if (languageCode !== this.DEFAULT_LANGUAGE) {
      const defaultTranslation = await this.getQuestionTranslation(questionId, this.DEFAULT_LANGUAGE);
      if (defaultTranslation) {
        return defaultTranslation;
      }
    }

    // Final fallback to original text
    return fallbackText;
  }

  /**
   * Get question option text with fallback logic
   */
  async getLocalizedOptionText(questionOptionId: string, languageCode: string, fallbackText: string): Promise<string> {
    // Try to get translation for requested language
    const translation = await this.getQuestionOptionTranslation(questionOptionId, languageCode);
    if (translation) {
      return translation;
    }

    // Fallback to default language if not the same as requested
    if (languageCode !== this.DEFAULT_LANGUAGE) {
      const defaultTranslation = await this.getQuestionOptionTranslation(questionOptionId, this.DEFAULT_LANGUAGE);
      if (defaultTranslation) {
        return defaultTranslation;
      }
    }

    // Final fallback to original text
    return fallbackText;
  }

  /**
   * Validate if language code is supported
   */
  async isLanguageSupported(languageCode: string): Promise<boolean> {
    const language = await this.getLanguageByCode(languageCode);
    return language !== null;
  }
}
