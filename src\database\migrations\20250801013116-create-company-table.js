'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('Companies', {
      id: {
        type: Sequelize.UUID,
        primaryKey: true,
        defaultValue: Sequelize.UUIDV4,
      },
      name: {
        type: Sequelize.STRING(150),
        allowNull: false,
      },
      product_owner_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'ProductOwners',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'NO ACTION',
      },
      // Uncomment if you plan to use company_admin_user_id in future
      // company_admin_user_id: {
      //   type: Sequelize.UUID,
      //   allowNull: false,
      //   references: {
      //     model: 'Users',
      //     key: 'id',
      //   },
      //   onUpdate: 'CASCADE',
      //   onDelete: 'NO ACTION',
      // },
      contact_person_firstName: {
        type: Sequelize.STRING(150),
        allowNull: false,
      },
      contact_person_lastName: {
        type: Sequelize.STRING(150),
        allowNull: false,
      },
      contact_person_email: {
        type: Sequelize.STRING(150),
        allowNull: false,
      },
      contact_person_phone: {
        type: Sequelize.STRING(20),
        allowNull: true,
      },
      address_line: {
        type: Sequelize.STRING(255),
        allowNull: false,
      },
      zipcode: {
        type: Sequelize.STRING(20),
        allowNull: false,
      },
      country: {
        type: Sequelize.STRING(100),
        allowNull: false,
      },
      state: {
        type: Sequelize.STRING(100),
        allowNull: false,
      },
      city: {
        type: Sequelize.STRING(100),
        allowNull: false,
      },
      isActive: {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
      },
      createdAt: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.fn('GETDATE'),
      },
      createdBy: {
        type: Sequelize.UUID,
      },
      updatedAt: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.fn('GETDATE'),
      },
      updatedBy: {
        type: Sequelize.UUID,
      },
      isDeleted: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      deletedBy: {
        type: Sequelize.UUID,
      },
    });
  },

  down: async (queryInterface) => {
    await queryInterface.dropTable('Companies');
  },
};
