import { Table, Column, Model, DataType, CreatedAt, UpdatedAt } from 'sequelize-typescript';

export enum ContactSubmissionStatus {
  NEW = 'new',
  READ = 'read',
  REPLIED = 'replied',
  CLOSED = 'closed'
}

@Table({
  tableName: 'ContactSubmissions',
  timestamps: true,
  paranoid: false
})
export class ContactSubmission extends Model<ContactSubmission> {
  @Column({
    type: DataType.UUID,
    defaultValue: DataType.UUIDV4,
    primaryKey: true,
    allowNull: false
  })
  declare id: string;

  @Column({
    type: DataType.STRING(100),
    allowNull: false,
    comment: 'Contact person first name'
  })
  firstName: string;

  @Column({
    type: DataType.STRING(100),
    allowNull: false,
    comment: 'Contact person last name'
  })
  lastName: string;

  @Column({
    type: DataType.STRING(255),
    allowNull: false,
    validate: {
      isEmail: true
    },
    comment: 'Contact email address'
  })
  email: string;

  @Column({
    type: DataType.STRING(20),
    allowNull: true,
    comment: 'Contact phone number'
  })
  phone: string | null;

  @Column({
    type: DataType.STRING(255),
    allowNull: false,
    comment: 'Contact subject/topic'
  })
  subject: string;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
    comment: 'Contact message content'
  })
  message: string;

  @Column({
    type: DataType.STRING(45),
    allowNull: true,
    comment: 'IP address of the submitter'
  })
  ipAddress: string;

  @Column({
    type: DataType.STRING(500),
    allowNull: true,
    comment: 'Browser user agent string'
  })
  userAgent: string;

  @Column({
    type: DataType.STRING(255),
    allowNull: true,
    comment: 'Domain that submitted the form'
  })
  refererDomain: string;

  @Column({
    type: DataType.ENUM(...Object.values(ContactSubmissionStatus)),
    defaultValue: ContactSubmissionStatus.NEW,
    comment: 'Status of the contact submission'
  })
  status: ContactSubmissionStatus;

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: false,
    comment: 'Whether this submission is marked as spam'
  })
  isSpam: boolean;

  @CreatedAt
  declare createdAt: Date;

  @UpdatedAt
  declare updatedAt: Date;

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: false
  })
  isDeleted: boolean;

  @Column({
    type: DataType.DATE,
    allowNull: true
  })
  declare deletedAt: Date;

  // Virtual field for full name
  get fullName(): string {
    return `${this.firstName} ${this.lastName}`;
  }
}
