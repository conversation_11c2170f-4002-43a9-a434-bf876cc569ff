#!/usr/bin/env node

/**
 * Sequential Seeder Runner for Employee Survey System
 * Runs seeders in proper dependency order
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🌱 Employee Survey System - Sequential Seeder Runner');
console.log('==================================================');

// Define seeder order based on dependencies
const seederOrder = [
  // Phase 1: Foundation Data (no dependencies)
  {
    phase: 'Foundation Data',
    seeders: [
      'create-roles.js',
      'create-languages.js', 
      'create-answer-categories.js'
    ]
  },
  
  // Phase 2: User Data (depends on roles)
  {
    phase: 'User Management',
    seeders: [
      'create-product-owner.js'
    ]
  },
  
  // Phase 3: Company Data (depends on product owners)
  {
    phase: 'Company Structure',
    seeders: [
      'create-companies.js',
      'create-departments.js'
    ]
  },
  
  // Phase 4: Question Data (depends on languages and categories)
  {
    phase: 'Survey Questions',
    seeders: [
      '20250726-employee-survey-questions.js'
    ]
  },
  
  // Phase 5: Sample Data (depends on all above)
  {
    phase: 'Sample Data',
    seeders: [
      'create-sample-users.js',
      'create-sample-responses.js'
    ]
  }
];

// Get all available seeder files
function getAvailableSeeders() {
  const seedersDir = path.join(process.cwd(), 'src', 'database', 'seeders');
  
  if (!fs.existsSync(seedersDir)) {
    console.error('❌ Seeders directory not found:', seedersDir);
    process.exit(1);
  }
  
  return fs.readdirSync(seedersDir)
    .filter(file => file.endsWith('.js'))
    .sort(); // Natural sort by timestamp
}

// Check seeder status
async function checkSeederStatus() {
  try {
    console.log('📋 Checking current seeder status...');
    const result = execSync('npx sequelize-cli db:seed:status', {
      encoding: 'utf8',
      cwd: process.cwd()
    });
    console.log(result);
    return true;
  } catch (error) {
    console.log('⚠️  Could not check seeder status');
    return false;
  }
}

// Run a single seeder
async function runSeeder(seederFile) {
  try {
    console.log(`🌱 Running: ${seederFile}`);
    
    execSync(`npx sequelize-cli db:seed --seed ${seederFile}`, {
      stdio: 'inherit',
      cwd: process.cwd()
    });
    
    console.log(`✅ Completed: ${seederFile}`);
    return true;
  } catch (error) {
    console.error(`❌ Failed: ${seederFile}`);
    console.error('Error:', error.message);
    return false;
  }
}

// Main execution function
async function runSeedersSequentially() {
  const availableSeeders = getAvailableSeeders();
  console.log(`📁 Found ${availableSeeders.length} seeder files`);
  
  // Check current status
  await checkSeederStatus();
  
  let totalSeeders = 0;
  let successfulSeeders = 0;
  let skippedSeeders = 0;
  
  // Process each phase
  for (const phase of seederOrder) {
    console.log(`\n🔄 Phase: ${phase.phase}`);
    console.log('─'.repeat(50));
    
    for (const seederName of phase.seeders) {
      totalSeeders++;
      
      // Find the actual seeder file (it might have a timestamp prefix)
      const actualSeederFile = availableSeeders.find(file => 
        file.includes(seederName) || file === seederName
      );
      
      if (!actualSeederFile) {
        console.log(`⚠️  Seeder not found: ${seederName} (skipping)`);
        skippedSeeders++;
        continue;
      }
      
      const success = await runSeeder(actualSeederFile);
      if (success) {
        successfulSeeders++;
      } else {
        console.error(`\n❌ Seeder failed: ${actualSeederFile}`);
        console.log('⚠️  Continuing with remaining seeders...');
        // Don't exit on seeder failure, continue with others
      }
    }
  }
  
  // Summary
  console.log('\n🎉 Seeder Summary');
  console.log('================');
  console.log(`✅ Successful: ${successfulSeeders}`);
  console.log(`⚠️  Skipped: ${skippedSeeders}`);
  console.log(`❌ Failed: ${totalSeeders - successfulSeeders - skippedSeeders}`);
  console.log(`📊 Total: ${totalSeeders}`);
  
  if (successfulSeeders > 0) {
    console.log('\n🎯 Seeding process completed!');
    console.log('\n📋 Next Steps:');
    console.log('1. Verify data: Check your database tables');
    console.log('2. Test API endpoints');
    console.log('3. Start your application: npm start');
  }
}

// Error handling
process.on('unhandledRejection', (error) => {
  console.error('❌ Unhandled error:', error);
  process.exit(1);
});

// Run the seeders
runSeedersSequentially().catch(error => {
  console.error('❌ Seeder process failed:', error);
  process.exit(1);
});
