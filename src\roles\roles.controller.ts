import {
  Controller,
  Get,
  UseGuards,
  BadRequestException,
} from '@nestjs/common';
import { RolesService } from './roles.service';
import { RoleListResponseDto } from './dto/role.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth-guards';
import { CurrentUser } from '../auth/current-user-decorator';

@Controller('api/roles')
@UseGuards(JwtAuthGuard)
export class RolesController {
  constructor(private readonly rolesService: RolesService) {}

  /**
   * Get all roles
   * GET /api/roles
   * Available to all authenticated users
   */
  @Get()
  async getRoles(
    @CurrentUser() user: any,
  ): Promise<RoleListResponseDto> {
    try {
      return await this.rolesService.getRoles();
    } catch (error) {
      throw new BadRequestException(`Failed to fetch roles: ${error.message}`);
    }
  }
}
