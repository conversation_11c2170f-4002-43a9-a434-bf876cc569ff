'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Remove the department_id column from Users table
    // This completes the migration to full junction table approach
    await queryInterface.removeColumn('Users', 'department_id');
    console.log('Removed department_id column from Users table');
  },

  async down(queryInterface, Sequelize) {
    // Add back the department_id column to Users table
    await queryInterface.addColumn('Users', 'department_id', {
      type: Sequelize.UUID,
      allowNull: true,
      references: {
        model: 'Departments',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });

    // Restore data from UserDepartments table back to Users table
    // This will only restore the first department for users with multiple departments
    const [userDepartments] = await queryInterface.sequelize.query(`
      SELECT DISTINCT ud.user_id, 
             FIRST_VALUE(ud.department_id) OVER (PARTITION BY ud.user_id ORDER BY ud.createdAt) as department_id
      FROM UserDepartments ud
      WHERE ud.isDeleted = 0 AND ud.isActive = 1
    `);

    for (const ud of userDepartments) {
      await queryInterface.sequelize.query(`
        UPDATE Users 
        SET department_id = :departmentId 
        WHERE id = :userId
      `, {
        replacements: {
          departmentId: ud.department_id,
          userId: ud.user_id
        }
      });
    }

    console.log(`Restored department_id for ${userDepartments.length} users`);
  }
};
