import { Injectable, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { CreationAttributes, Op } from 'sequelize';
import { Department } from '../database/models/department.model';
import { Company } from '../database/models/company.model';
import { User } from '../database/models/user.model';
import { CreateDepartmentDto, UpdateDepartmentDto, DepartmentResponseDto } from './dto/department.dto';

@Injectable()
export class DepartmentsService {
  constructor(
    @InjectModel(Department)
    private readonly departmentModel: typeof Department,
    @InjectModel(Company)
    private readonly companyModel: typeof Company,
    @InjectModel(User)
    private readonly userModel: typeof User,
  ) {}

  /**
   * Create a new department (CompanyAdmin only)
   */
  async createDepartment(
    companyId: string,
    dto: CreateDepartmentDto,
    userId: string,
  ): Promise<DepartmentResponseDto> {
    // Verify company exists
    const company = await this.companyModel.findOne({
      where: { id: companyId, isDeleted: false },
    });

    if (!company) {
      throw new NotFoundException('Company not found');
    }

    // Check if department name already exists in the company
    const existingDepartment = await this.departmentModel.findOne({
      where: {
        name: dto.name,
        company_id: companyId,
        isDeleted: false,
      },
    });

    if (existingDepartment) {
      throw new BadRequestException(`Department with name '${dto.name}' already exists in this company`);
    }

    // Create department
    const department = await this.departmentModel.create({
      ...dto,
      company_id: companyId,
      createdBy: userId,
    } as CreationAttributes<Department>);

    return this.mapToResponseDto(department);
  }

  /**
   * Update department (CompanyAdmin only)
   */
  async updateDepartment(
    id: string,
    companyId: string,
    dto: UpdateDepartmentDto,
    userId: string,
  ): Promise<DepartmentResponseDto> {
    const department = await this.departmentModel.findOne({
      where: { id, isDeleted: false },
    });

    if (!department) {
      throw new NotFoundException('Department not found');
    }

    // Verify department belongs to the company
    if (department.company_id !== companyId) {
      throw new ForbiddenException('You can only update departments within your company');
    }

    // If updating name, check for duplicates
    if (dto.name && dto.name !== department.name) {
      const existingDepartment = await this.departmentModel.findOne({
        where: {
          name: dto.name,
          company_id: companyId,
          isDeleted: false,
          id: { [Op.ne]: id },
        },
      });

      if (existingDepartment) {
        throw new BadRequestException(`Department with name '${dto.name}' already exists in this company`);
      }
    }

    // Update department
    await department.update({
      ...dto,
      updatedBy: userId,
    });

    return this.mapToResponseDto(department);
  }

  /**
   * Get departments by company (any authenticated user)
   */
  async getDepartmentsByCompany(companyId: string): Promise<DepartmentResponseDto[]> {
    const departments = await this.departmentModel.findAll({
      where: {
        company_id: companyId,
        isDeleted: false,
      },
      order: [['name', 'ASC']],
    });

    return departments.map(dept => this.mapToResponseDto(dept));
  }

  /**
   * Get department by ID (any authenticated user)
   */
  async getDepartmentById(id: string, companyId: string): Promise<DepartmentResponseDto> {
    const department = await this.departmentModel.findOne({
      where: {
        id,
        company_id: companyId,
        isDeleted: false,
      },
    });

    if (!department) {
      throw new NotFoundException('Department not found');
    }

    return this.mapToResponseDto(department);
  }

  /**
   * Delete department (CompanyAdmin only)
   * Also deletes all users attached to this department
   */
  async deleteDepartment(id: string, companyId: string, userId: string): Promise<{ message: string }> {
    const department = await this.departmentModel.findOne({
      where: { id, isDeleted: false },
    });

    if (!department) {
      throw new NotFoundException('Department not found');
    }

    const simplifiedDepartments = department.get({plain: true})

    console.log("companyId", companyId)
    console.log("department.company_id", simplifiedDepartments)

    // Verify department belongs to the company
    if (simplifiedDepartments.company_id !== companyId) {
      throw new ForbiddenException('You can only delete departments within your company');
    }

    // Get count of users in this department for logging
    const usersInDepartment = await this.userModel.count({
      where: {
        department_id: id,
        isDeleted: false,
      },
    });

    console.log(`Found ${usersInDepartment} users in department ${simplifiedDepartments.name} to be deleted`);

    // Soft delete all users in this department
    await this.userModel.update(
      {
        isDeleted: true,
        deletedBy: userId,
        isActive: false, // Also deactivate the users
      },
      {
        where: {
          department_id: id,
          isDeleted: false,
        },
      }
    );

    // Soft delete the department
    await department.update({
      isDeleted: true,
      deletedBy: userId,
    });

    return {
      message: `Department deleted successfully. ${usersInDepartment} users were also deleted from this department.`
    };
  }

  /**
   * Map Department model to response DTO
   */
  private mapToResponseDto(department: Department): DepartmentResponseDto {
    // Use get() method to ensure we get the actual values
    const deptData = department.get({ plain: true });

    return {
      id: deptData.id,
      name: deptData.name,
      description: deptData.description,
      company_id: deptData.company_id,
      isActive: deptData.isActive,
      createdAt: deptData.createdAt,
      updatedAt: deptData.updatedAt,
      createdBy: deptData.createdBy,
      updatedBy: deptData.updatedBy,
    };
  }
}
