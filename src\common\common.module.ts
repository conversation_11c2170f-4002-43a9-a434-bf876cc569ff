import { Modu<PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { TranslationService } from './services/translation.service';
import { EmailService } from './services/email.service';
import { Language } from '../database/models/language.model';
import { QuestionTranslation } from '../database/models/question-translation.model';
import { QuestionOptionTranslation } from '../database/models/question-option-translation.model';

@Module({
  imports: [
    SequelizeModule.forFeature([
      Language,
      QuestionTranslation,
      QuestionOptionTranslation
    ])
  ],
  providers: [
    TranslationService,
    EmailService
  ],
  exports: [
    TranslationService,
    EmailService
  ]
})
export class CommonModule {}
