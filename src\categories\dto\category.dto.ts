import { IsString, <PERSON><PERSON>otEmpt<PERSON>, IsOptional, IsBoolean, IsInt, IsHexColor, MaxLength, Min } from 'class-validator';

export class CreateCategoryDto {
  @IsString()
  @IsNotEmpty()
  @MaxLength(50)
  name: string;

  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  display_name: string;

  @IsString()
  @IsHexColor()
  color_code: string;

  @IsInt()
  @Min(0)
  sort_order: number;

  @IsString()
  @IsOptional()
  description?: string;

  @IsBoolean()
  @IsOptional()
  isActive?: boolean = true;

  @IsString()
  @IsOptional()
  createdBy?: string;
}

export class UpdateCategoryDto {
  @IsString()
  @IsOptional()
  @MaxLength(50)
  name?: string;

  @IsString()
  @IsOptional()
  @MaxLength(100)
  display_name?: string;

  @IsString()
  @IsHexColor()
  @IsOptional()
  color_code?: string;

  @IsInt()
  @Min(0)
  @IsOptional()
  sort_order?: number;

  @IsString()
  @IsOptional()
  description?: string;

  @IsBoolean()
  @IsOptional()
  isActive?: boolean;

  @IsString()
  @IsOptional()
  updatedBy?: string;
}

export class CategoryResponseDto {
  id: string;
  name: string;
  display_name: string;
  color_code: string;
  sort_order: number;
  description?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  questionOptionsCount?: number;
}

export class CategoryUsageStatsDto {
  id: string;
  name: string;
  display_name: string;
  color_code: string;
  sort_order: number;
  questionOptionsCount: number;
  responseCount: number;
  lastUsed?: Date;
}
