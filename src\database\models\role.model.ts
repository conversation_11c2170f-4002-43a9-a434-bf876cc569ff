// src/core/database/models/role.model.ts

import {
  Table,
  Column,
  Model,
  DataType,
  PrimaryKey,
  <PERSON>fault,
  AllowNull,
  CreatedAt,
  UpdatedAt,
  HasMany,
} from 'sequelize-typescript';
import { ProductOwner } from './product-owner-model'; // Adjust the import path as necessary
@Table({
  tableName: 'Roles',
  timestamps: false,
})
export class Role extends Model<Role> {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column(DataType.UUID)
  declare id: string;

  @AllowNull(false)
  @Column(DataType.STRING)
  name: string;

  @Column(DataType.STRING)
  description?: string;

  @Default(true)
  @Column(DataType.BOOLEAN)
  isActive: boolean;

  @Default(false)
  @Column(DataType.BOOLEAN)
  isDeleted: boolean;

  @CreatedAt
  @Column(DataType.DATE)
  declare createdAt: Date;

  @UpdatedAt
  @Column(DataType.DATE)
  declare updatedAt: Date;

  @HasMany(() => ProductOwner)
  productOwners: ProductOwner[];
}
