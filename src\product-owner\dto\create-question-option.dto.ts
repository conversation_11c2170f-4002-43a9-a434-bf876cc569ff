import { IsString, <PERSON><PERSON>otE<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsUUID } from 'class-validator';
import { IsTranslationOrString, TranslationObject } from '../../common/validators/translation.validator';

export class CreateQuestionOptionDto {
  @IsTranslationOrString({
    message: 'option_text must be either a non-empty string or a translation object with at least English (en) translation'
  })
  option_text: string | TranslationObject;

  @IsInt()
  @Min(0)
  @Max(100)
  option_value: number;

  @IsUUID('4')
  @IsNotEmpty()
  categoryId: string;

  @IsOptional()
  @IsUUID('4')
  createdBy?: string;
}