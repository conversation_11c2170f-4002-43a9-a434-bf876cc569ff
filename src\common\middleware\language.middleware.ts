import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';

// Extend the Request interface to include language
declare global {
  namespace Express {
    interface Request {
      language?: string;
    }
  }
}

@Injectable()
export class LanguageMiddleware implements NestMiddleware {
  private readonly DEFAULT_LANGUAGE = 'en';
  private readonly SUPPORTED_LANGUAGES = ['en', 'es']; // English, Spanish

  use(req: Request, res: Response, next: NextFunction) {
    // Extract language from headers
    // Priority: X-Language header > Accept-Language header > default
    let language = this.DEFAULT_LANGUAGE;

    // Check for custom X-Language header first
    const customLanguage = req.headers['x-language'] as string;
    if (customLanguage && this.isLanguageSupported(customLanguage)) {
      language = customLanguage.toLowerCase();
    } else {
      // Fallback to Accept-Language header
      const acceptLanguage = req.headers['accept-language'] as string;
      if (acceptLanguage) {
        const preferredLanguage = this.parseAcceptLanguage(acceptLanguage);
        if (preferredLanguage && this.isLanguageSupported(preferredLanguage)) {
          language = preferredLanguage;
        }
      }
    }

    // Set the language in the request object for use in controllers/services
    req.language = language;

    next();
  }

  private isLanguageSupported(language: string): boolean {
    return this.SUPPORTED_LANGUAGES.includes(language.toLowerCase());
  }

  private parseAcceptLanguage(acceptLanguage: string): string | null {
    // Parse Accept-Language header (e.g., "en-US,en;q=0.9,es;q=0.8")
    // Return the first supported language
    const languages = acceptLanguage
      .split(',')
      .map(lang => {
        const [code] = lang.trim().split(';');
        return code.split('-')[0].toLowerCase(); // Extract base language code
      });

    for (const lang of languages) {
      if (this.isLanguageSupported(lang)) {
        return lang;
      }
    }

    return null;
  }
}
