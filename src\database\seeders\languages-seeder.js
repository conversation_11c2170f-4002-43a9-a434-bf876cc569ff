'use strict';

const { v4: uuidv4 } = require('uuid');

/** @param {import('sequelize').QueryInterface} queryInterface */
module.exports = {
  up: async (queryInterface) => {
    try {
      const now = new Date();

      await queryInterface.bulkInsert('Languages', [
        {
          id: uuidv4(),
          code: 'en',
          name: 'English',
          isDefault: true,
          isActive: true,
          isDeleted: false,
          createdAt: now,
          updatedAt: now,
        },
        {
          id: uuidv4(),
          code: 'es',
          name: 'Spanish',
          isDefault: false,
          isActive: true,
          isDeleted: false,
          createdAt: now,
          updatedAt: now,
        },
      ]);

      console.log('Languages seeded!');
    } catch (err) {
      console.error('Languages seeding failed:', err);
    }
  },

  down: async (queryInterface) => {
    await queryInterface.bulkDelete('Languages', null, {});
  },
};
