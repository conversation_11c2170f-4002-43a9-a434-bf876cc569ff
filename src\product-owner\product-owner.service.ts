import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';

import { CreationAttributes, Op } from 'sequelize';
import { Company } from 'src/database/models/company.model';
import { CreateCompanyDto } from './dto/create-company-dto';
import { CompanyListResponseDto, CompanyResponseDto } from './dto/company-response.dto';
import { User } from 'src/database/models/user.model';
import { Role } from 'src/database/models/role.model'; // adjust the path if needed
import * as bcrypt from 'bcrypt';

@Injectable()
export class ProductOwnerService {
  // constructor(
  //   @InjectModel(Company)
  //   private readonly companyModel: typeof Company,
  // ) {}

  constructor(
    @InjectModel(Company) private companyModel: typeof Company,
    @InjectModel(User) private userModel: typeof User,
    @InjectModel(Role) private roleModel: typeof Role, // 👈 Inject Role model
    // private mailService: MailService,
  ) { }

  async createCompany(dto: CreateCompanyDto): Promise<{ message: string, company: Company }> {
    // Optional: Add extra metadata like createdBy from the logged-in user
    const company = await this.companyModel.create(dto as CreationAttributes<Company>);

    const tempPassword = Math.random().toString(36).slice(-8);
    // const hashedPassword = await bcrypt.hash(tempPassword, 10);

    const hashedPassword = await bcrypt.hash('umavanshi@1101', 10);

    const superAdminRole = await this.roleModel.findOne({
      where: { name: 'CompanyAdmin', isDeleted: false },
    });

    if (!superAdminRole) {
      throw new Error('SuperAdmin role not found');
    }

    // Fix: Use the correct field names from your User model
    const adminUser = await this.userModel.create({
      firstName: dto.contact_person_firstName,
      lastName: dto.contact_person_lastName,
      email: dto.contact_person_email,
      password: hashedPassword,
      roleId: superAdminRole.id,
      isActive: true,
      company_id: company.id, // Link the user to the company
    } as CreationAttributes<User>);

    // Optionally, you can send an email to the admin user with the temporary password
    // await this.mailService.sendWelcomeEmail(adminUser.email, tempPassword);

    return { message: 'Company and admin user created successfully', company };
  }

  async getCompanies(filters?: {
    page?: number;
    limit?: number;
    search?: string;
    sortBy?: string;
    sortOrder?: 'ASC' | 'DESC';
    isActive?: boolean;
    country?: string;
    state?: string;
    city?: string;
  }): Promise<CompanyListResponseDto> {
    // Build where clause
    const whereClause: any = {
      isDeleted: false,
    };

    // Apply filters
    if (filters?.isActive !== undefined) {
      whereClause.isActive = filters.isActive;
    }

    if (filters?.country) {
      whereClause.country = {
        [Op.like]: `%${filters.country}%`
      };
    }

    if (filters?.state) {
      whereClause.state = {
        [Op.like]: `%${filters.state}%`
      };
    }

    if (filters?.city) {
      whereClause.city = {
        [Op.like]: `%${filters.city}%`
      };
    }

    // Search functionality
    if (filters?.search) {
      whereClause[Op.or] = [
        { name: { [Op.like]: `%${filters.search}%` } },
        { contact_person_firstName: { [Op.like]: `%${filters.search}%` } },
        { contact_person_lastName: { [Op.like]: `%${filters.search}%` } },
        { contact_person_email: { [Op.like]: `%${filters.search}%` } },
        { address_line: { [Op.like]: `%${filters.search}%` } },
        { zipcode: { [Op.like]: `%${filters.search}%` } },
      ];
    }

    // Sorting
    const validSortFields = ['name', 'createdAt', 'updatedAt', 'country', 'state', 'city', 'contact_person_firstName', 'contact_person_lastName'];
    const sortBy = filters?.sortBy && validSortFields.includes(filters.sortBy) ? filters.sortBy : 'createdAt';
    const sortOrder = filters?.sortOrder === 'ASC' ? 'ASC' : 'DESC';

    // Pagination
    const page = filters?.page || 1;
    const limit = filters?.limit || 10;
    const offset = (page - 1) * limit;

    const { rows: companies, count: total } = await this.companyModel.findAndCountAll({
      where: whereClause,
      limit,
      offset,
      order: [[sortBy, sortOrder]],
    });

    // Convert to plain objects and map to response DTOs
    const simplifiedCompanies = companies.map(company => company.get({ plain: true }));
    const companyResponses = simplifiedCompanies.map((company: any) =>
      this.mapToResponseDto(company)
    );

    return {
      companies: companyResponses,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  /**
   * Map Company model to response DTO
   */
  private mapToResponseDto(company: any): CompanyResponseDto {
    return {
      id: company.id,
      name: company.name,
      product_owner_id: company.product_owner_id,
      contact_person_firstName: company.contact_person_firstName,
      contact_person_lastName: company.contact_person_lastName,
      contact_person_email: company.contact_person_email,
      contact_person_phone: company.contact_person_phone,
      address_line: company.address_line,
      zipcode: company.zipcode,
      country: company.country,
      state: company.state,
      city: company.city,
      isActive: company.isActive,
      createdAt: company.createdAt,
      updatedAt: company.updatedAt,
      createdBy: company.createdBy,
      updatedBy: company.updatedBy,
    };
  }

  async getCompanyById(id: string): Promise<Company> {
    const company = await this.companyModel.findOne({
      where: { id, isDeleted: false }
    });

    if (!company) {
      throw new Error(`Company with ID ${id} not found`);
    }

    return company;
  }
}
