import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { ReportsController } from './reports.controller';
import { ReportsService } from './reports.service';
import { SurveyResponse } from '../database/models/survey-response.model';
import { AnswerCategory } from '../database/models/answer-category.model';
import { QuestionOption } from '../database/models/question-option.model';
import { Question } from '../database/models/question.model';
import { User } from '../database/models/user.model';
import { Company } from '../database/models/company.model';
import { Department } from '../database/models/department.model';
import { Report } from '../database/models/report.model';

@Module({
  imports: [
    SequelizeModule.forFeature([
      SurveyResponse,
      AnswerCategory,
      QuestionOption,
      Question,
      User,
      Company,
      Department,
      Report
    ])
  ],
  controllers: [ReportsController],
  providers: [ReportsService],
  exports: [ReportsService]
})
export class ReportsModule {}
