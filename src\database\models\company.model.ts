// src/core/database/models/company.model.ts

import {
  Table,
  Column,
  Model,
  DataType,
  PrimaryKey,
  Default,
  AllowNull,
  ForeignKey,
  CreatedAt,
  UpdatedAt,
} from 'sequelize-typescript';
import { User } from './user.model';
import { ProductOwner } from './product-owner-model';

@Table({
  tableName: 'Companies',
})
export class Company extends Model<Company> {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column({ type: DataType.UUID })
  declare id: string;

  @AllowNull(false)
  @Column(DataType.STRING(150))
  name: string;

  @ForeignKey(() => ProductOwner)
  @AllowNull(false)
  @Column(DataType.UUID)
  product_owner_id: string;

  // @ForeignKey(() => User)
  // @AllowNull(false)
  // @Column(DataType.UUID)
  // company_admin_user_id: string;

  @AllowNull(false)
  @Column(DataType.STRING(150))
  contact_person_firstName: string;

  @AllowNull(false)
  @Column(DataType.STRING(150))
  contact_person_lastName: string;

  @AllowNull(false)
  @Column(DataType.STRING(150))
  contact_person_email: string;

  @AllowNull(true)
  @Column(DataType.STRING(20))
  contact_person_phone: string;

  @AllowNull(false)
  @Column(DataType.STRING(255))
  address_line: string;

  @AllowNull(false)
  @Column(DataType.STRING(20))
  zipcode: string;

  @AllowNull(false)
  @Column(DataType.STRING(100))
  country: string;

  @AllowNull(false)
  @Column(DataType.STRING(100))
  state: string;

  @AllowNull(false)
  @Column(DataType.STRING(100))
  city: string;

  @Default(true)
  @Column(DataType.BOOLEAN)
  isActive: boolean;

  @CreatedAt
  @Default(DataType.NOW)
  @Column(DataType.DATE)
  declare createdAt: Date;

  @Column(DataType.UUID)
  createdBy: string;

  @UpdatedAt
  @Default(DataType.NOW)
  @Column(DataType.DATE)
  declare updatedAt: Date;

  @Column(DataType.UUID)
  updatedBy: string;

  @Default(false)
  @Column(DataType.BOOLEAN)
  isDeleted: boolean;

  @Column(DataType.UUID)
  deletedBy: string;
}
