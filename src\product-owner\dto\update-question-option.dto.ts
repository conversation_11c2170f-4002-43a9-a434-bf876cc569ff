import { IsS<PERSON>, IsNotEmpty, IsInt, <PERSON>, <PERSON>, IsO<PERSON>al, IsBoolean, IsUUID } from 'class-validator';

export class UpdateQuestionOptionDto {
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  option_text?: string;

  @IsOptional()
  @IsInt()
  @Min(0)
  @Max(100)
  option_value?: number;

  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @IsOptional()
  @IsUUID('4')
  updatedBy?: string;
}