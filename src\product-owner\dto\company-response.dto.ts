export class CompanyResponseDto {
  id: string;
  name: string;
  product_owner_id: string;
  contact_person_firstName: string;
  contact_person_lastName: string;
  contact_person_email: string;
  contact_person_phone?: string;
  address_line: string;
  zipcode: string;
  country: string;
  state: string;
  city: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  createdBy?: string;
  updatedBy?: string;
}

export class CompanyListResponseDto {
  companies: CompanyResponseDto[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}
