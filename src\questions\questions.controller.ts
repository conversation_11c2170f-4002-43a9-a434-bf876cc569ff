import {
  Controller,
  Get,
  UseGuards,
  BadRequestException,
  Req,
} from '@nestjs/common';
import { Request } from 'express';
import { QuestionsService } from './questions.service';
import { RandomQuestionsResponseDto } from './dto/question.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth-guards';
import { CurrentUser } from '../auth/current-user-decorator';

@Controller('api/questions')
@UseGuards(JwtAuthGuard)
export class QuestionsController {
  constructor(private readonly questionsService: QuestionsService) {}

  /**
   * Get 2 random questions for employees from their company
   * GET /api/questions
   * Available to all authenticated users
   */
  @Get()
  async getRandomQuestions(
    @CurrentUser() user: any,
    @Req() req: Request
  ): Promise<RandomQuestionsResponseDto> {
    try {
      if (!user.companyId) {
        throw new BadRequestException('User does not have a company assigned');
      }

      const language = req.language || 'en';
      return await this.questionsService.getRandomQuestionsForCompany(language);
    } catch (error) {
      throw new BadRequestException(`Failed to fetch questions: ${error.message}`);
    }
  }
}
