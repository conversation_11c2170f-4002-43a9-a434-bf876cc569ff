#!/usr/bin/env node

/**
 * Test Migration System
 * Validates the model-to-migration generation system
 */

const fs = require('fs');
const path = require('path');
const { analyzeModels } = require('./analyze-models');

console.log('🧪 Testing Migration System...');
console.log('==============================');

async function testSystem() {
  const results = {
    modelAnalysis: false,
    dependencyResolution: false,
    migrationGeneration: false,
    fileStructure: false
  };

  try {
    // Test 1: Model Analysis
    console.log('\n📋 Test 1: Model Analysis');
    console.log('─'.repeat(30));
    
    const modelsPath = path.join(__dirname, '../src/database/models');
    if (!fs.existsSync(modelsPath)) {
      throw new Error(`Models directory not found: ${modelsPath}`);
    }

    const modelFiles = fs.readdirSync(modelsPath)
      .filter(file => file.endsWith('.model.ts'));
    
    console.log(`✅ Found ${modelFiles.length} model files`);
    modelFiles.forEach(file => console.log(`   - ${file}`));
    results.modelAnalysis = true;

    // Test 2: Dependency Resolution
    console.log('\n🔗 Test 2: Dependency Resolution');
    console.log('─'.repeat(35));
    
    const analysis = analyzeModels();
    console.log(`✅ Resolved ${analysis.orderedModels.length} models in dependency order`);
    
    // Check for circular dependencies
    const modelNames = analysis.orderedModels.map(m => m.fileName);
    const hasDuplicates = modelNames.length !== new Set(modelNames).size;
    if (hasDuplicates) {
      throw new Error('Circular dependency detected!');
    }
    console.log('✅ No circular dependencies found');
    results.dependencyResolution = true;

    // Test 3: Migration Generation (dry run)
    console.log('\n🏗️ Test 3: Migration Generation (Dry Run)');
    console.log('─'.repeat(45));
    
    const outputPath = path.join(__dirname, '../src/database/migrations-generated');
    console.log(`✅ Output path configured: ${outputPath}`);
    
    // Check if we can write to output directory
    if (!fs.existsSync(path.dirname(outputPath))) {
      fs.mkdirSync(path.dirname(outputPath), { recursive: true });
    }
    console.log('✅ Output directory accessible');
    results.migrationGeneration = true;

    // Test 4: File Structure Validation
    console.log('\n📁 Test 4: File Structure Validation');
    console.log('─'.repeat(40));
    
    const requiredFiles = [
      'scripts/analyze-models.js',
      'scripts/generate-migrations-from-models.js',
      'scripts/run-migrations-sequential.js',
      'scripts/package-scripts-addition.json',
      'docs/MODEL-TO-MIGRATION-SYSTEM.md'
    ];

    for (const file of requiredFiles) {
      const filePath = path.join(__dirname, '..', file);
      if (fs.existsSync(filePath)) {
        console.log(`✅ ${file}`);
      } else {
        console.log(`❌ ${file} - MISSING`);
        throw new Error(`Required file missing: ${file}`);
      }
    }
    results.fileStructure = true;

    // Test Summary
    console.log('\n🎉 Test Summary');
    console.log('─'.repeat(20));
    console.log(`Model Analysis: ${results.modelAnalysis ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Dependency Resolution: ${results.dependencyResolution ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Migration Generation: ${results.migrationGeneration ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`File Structure: ${results.fileStructure ? '✅ PASS' : '❌ FAIL'}`);

    const allPassed = Object.values(results).every(result => result === true);
    
    if (allPassed) {
      console.log('\n🎊 ALL TESTS PASSED!');
      console.log('✅ Migration system is ready to use');
      console.log('\n📋 Next Steps:');
      console.log('1. Add scripts to package.json (see scripts/package-scripts-addition.json)');
      console.log('2. Run: npm run generate:migrations-from-models');
      console.log('3. Run: npm run db:migrate:sequential');
    } else {
      console.log('\n❌ SOME TESTS FAILED');
      console.log('Please fix the issues above before using the migration system');
    }

    return allPassed;

  } catch (error) {
    console.error('\n💥 Test failed:', error.message);
    return false;
  }
}

// Additional validation functions
function validateModelStructure() {
  console.log('\n🔍 Additional Validation: Model Structure');
  console.log('─'.repeat(45));
  
  const modelsPath = path.join(__dirname, '../src/database/models');
  const modelFiles = fs.readdirSync(modelsPath)
    .filter(file => file.endsWith('.model.ts'));

  const expectedModels = [
    'role.model.ts',
    'user.model.ts',
    'company.model.ts',
    'department.model.ts',
    'question.model.ts',
    'question-option.model.ts',
    'survey-response.model.ts'
  ];

  const foundModels = [];
  const missingModels = [];

  for (const expected of expectedModels) {
    if (modelFiles.includes(expected)) {
      foundModels.push(expected);
      console.log(`✅ ${expected}`);
    } else {
      missingModels.push(expected);
      console.log(`⚠️  ${expected} - Not found (optional)`);
    }
  }

  console.log(`\n📊 Model Summary:`);
  console.log(`   Found: ${foundModels.length} expected models`);
  console.log(`   Total: ${modelFiles.length} model files`);
  console.log(`   Missing: ${missingModels.length} expected models`);

  return { foundModels, missingModels, totalModels: modelFiles.length };
}

function showUsageInstructions() {
  console.log('\n📖 Usage Instructions');
  console.log('─'.repeat(25));
  console.log('1. Add scripts to package.json:');
  console.log('   Copy from: scripts/package-scripts-addition.json');
  console.log('');
  console.log('2. Generate migrations:');
  console.log('   npm run generate:migrations-from-models');
  console.log('');
  console.log('3. Run migrations:');
  console.log('   npm run db:migrate:sequential');
  console.log('');
  console.log('4. Check status:');
  console.log('   npm run db:migrate:sequential:status');
  console.log('');
  console.log('📚 Full documentation: docs/MODEL-TO-MIGRATION-SYSTEM.md');
}

// Run tests
if (require.main === module) {
  testSystem()
    .then(success => {
      if (success) {
        validateModelStructure();
        showUsageInstructions();
      }
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('💥 Test execution failed:', error);
      process.exit(1);
    });
}

module.exports = { testSystem, validateModelStructure };
