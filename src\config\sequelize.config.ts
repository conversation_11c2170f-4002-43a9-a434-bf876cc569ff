import { SequelizeModuleOptions } from '@nestjs/sequelize';

export const sequelizeConfig: SequelizeModuleOptions = {
  dialect: 'mssql',
  host: 'localhost',
  port: 1433,
  username: "pratik_1101",
  password: 'umavanshi@1101',
  database: "VE",
  dialectOptions: {
    options: {
      encrypt: true,
      trustServerCertificate: true,
    },
    instanceName: 'SQLEXPRESS',
  },
  autoLoadModels: true,
  synchronize: false,
  logging: console.log, 
};

