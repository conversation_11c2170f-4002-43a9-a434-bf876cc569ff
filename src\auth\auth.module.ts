// src/auth/auth.module.ts
import { Module } from '@nestjs/common';
import { AuthService } from './auth.service';
import { JwtModule } from '@nestjs/jwt';
import { SequelizeModule } from '@nestjs/sequelize';
import { jwtConstants } from 'src/common/constants/jwt.constants';
import { JwtStrategy } from 'src/common/strategies/jwt.strategies';
import { UserAuthController } from './user-auth.controller';
import { ProductOwnerAuthController } from './auth.controller';
import { UserAuthService } from './user-auth.service';
import { User } from 'src/database/models/user.model';
import { Role } from 'src/database/models/role.model';
import { ProductOwner } from 'src/database/models/product-owner-model';
import { UserDepartment } from 'src/database/models/user-department.model';
import { RolesGuard } from './roles.guard';

@Module({
  imports: [
    JwtModule.register({
      secret: jwtConstants.secret,
      signOptions: { expiresIn: '1d' },
    }),
    SequelizeModule.forFeature([User, Role, ProductOwner, UserDepartment]),
  ],
  providers: [
    AuthService, 
    JwtStrategy, 
    UserAuthService,
    RolesGuard, // Make sure RolesGuard is provided here
  ],
  controllers: [UserAuthController, ProductOwnerAuthController],
  exports: [RolesGuard], // Export it so it can be used in other modules
})
export class AuthModule {}



