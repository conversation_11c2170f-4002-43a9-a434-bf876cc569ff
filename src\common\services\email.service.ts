import { Injectable } from '@nestjs/common';

export interface EmailTemplate {
  to: string;
  subject: string;
  html: string;
  text?: string;
}

@Injectable()
export class EmailService {
  /**
   * Mock email service for sending temporary passwords to new users
   * In production, this would integrate with actual email service (SendGrid, AWS SES, etc.)
   */
  
  async sendWelcomeEmail(
    email: string, 
    firstName: string, 
    lastName: string, 
    temporaryPassword: string,
    companyName: string
  ): Promise<void> {
    const emailTemplate: EmailTemplate = {
      to: email,
      subject: `Welcome to ${companyName} - Your Account Details`,
      html: this.generateWelcomeEmailHtml(firstName, lastName, email, temporaryPassword, companyName),
      text: this.generateWelcomeEmailText(firstName, lastName, email, temporaryPassword, companyName)
    };

    // Mock email sending - in production, replace with actual email service
    console.log('📧 MOCK EMAIL SENT:');
    console.log('===================');
    console.log(`To: ${emailTemplate.to}`);
    console.log(`Subject: ${emailTemplate.subject}`);
    console.log(`Content:\n${emailTemplate.text}`);
    console.log('===================');

    // Simulate email sending delay
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  async sendPasswordResetEmail(
    email: string,
    firstName: string,
    temporaryPassword: string,
    companyName: string
  ): Promise<void> {
    const emailTemplate: EmailTemplate = {
      to: email,
      subject: `${companyName} - Password Reset`,
      html: this.generatePasswordResetEmailHtml(firstName, email, temporaryPassword, companyName),
      text: this.generatePasswordResetEmailText(firstName, email, temporaryPassword, companyName)
    };

    // Mock email sending
    console.log('📧 MOCK PASSWORD RESET EMAIL SENT:');
    console.log('===================================');
    console.log(`To: ${emailTemplate.to}`);
    console.log(`Subject: ${emailTemplate.subject}`);
    console.log(`Content:\n${emailTemplate.text}`);
    console.log('===================================');

    await new Promise(resolve => setTimeout(resolve, 100));
  }

  private generateWelcomeEmailHtml(
    firstName: string,
    lastName: string,
    email: string,
    temporaryPassword: string,
    companyName: string
  ): string {
    return `
      <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
          <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
            <h2 style="color: #2c3e50;">Welcome to ${companyName}!</h2>
            
            <p>Dear ${firstName} ${lastName},</p>
            
            <p>Your account has been created successfully. Here are your login credentials:</p>
            
            <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
              <p><strong>Email:</strong> ${email}</p>
              <p><strong>Temporary Password:</strong> <code style="background-color: #e9ecef; padding: 2px 4px; border-radius: 3px;">${temporaryPassword}</code></p>
            </div>
            
            <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;">
              <p><strong>⚠️ Important:</strong> You must change your password on first login for security reasons.</p>
            </div>
            
            <p>Please log in to the system and change your password immediately.</p>
            
            <p>If you have any questions, please contact your system administrator.</p>
            
            <p>Best regards,<br>
            ${companyName} Team</p>
          </div>
        </body>
      </html>
    `;
  }

  private generateWelcomeEmailText(
    firstName: string,
    lastName: string,
    email: string,
    temporaryPassword: string,
    companyName: string
  ): string {
    return `
Welcome to ${companyName}!

Dear ${firstName} ${lastName},

Your account has been created successfully. Here are your login credentials:

Email: ${email}
Temporary Password: ${temporaryPassword}

⚠️ IMPORTANT: You must change your password on first login for security reasons.

Please log in to the system and change your password immediately.

If you have any questions, please contact your system administrator.

Best regards,
${companyName} Team
    `.trim();
  }

  private generatePasswordResetEmailHtml(
    firstName: string,
    email: string,
    temporaryPassword: string,
    companyName: string
  ): string {
    return `
      <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
          <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
            <h2 style="color: #2c3e50;">Password Reset - ${companyName}</h2>
            
            <p>Dear ${firstName},</p>
            
            <p>Your password has been reset. Here are your new login credentials:</p>
            
            <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
              <p><strong>Email:</strong> ${email}</p>
              <p><strong>Temporary Password:</strong> <code style="background-color: #e9ecef; padding: 2px 4px; border-radius: 3px;">${temporaryPassword}</code></p>
            </div>
            
            <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;">
              <p><strong>⚠️ Important:</strong> You must change this temporary password on your next login.</p>
            </div>
            
            <p>Please log in and change your password immediately.</p>
            
            <p>If you did not request this password reset, please contact your system administrator immediately.</p>
            
            <p>Best regards,<br>
            ${companyName} Team</p>
          </div>
        </body>
      </html>
    `;
  }

  private generatePasswordResetEmailText(
    firstName: string,
    email: string,
    temporaryPassword: string,
    companyName: string
  ): string {
    return `
Password Reset - ${companyName}

Dear ${firstName},

Your password has been reset. Here are your new login credentials:

Email: ${email}
Temporary Password: ${temporaryPassword}

⚠️ IMPORTANT: You must change this temporary password on your next login.

Please log in and change your password immediately.

If you did not request this password reset, please contact your system administrator immediately.

Best regards,
${companyName} Team
    `.trim();
  }

  /**
   * Generate a random temporary password
   */
  generateTemporaryPassword(): string {
    const length = 12;
    const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
    let password = '';
    
    // Ensure at least one of each type
    password += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'[Math.floor(Math.random() * 26)]; // Uppercase
    password += 'abcdefghijklmnopqrstuvwxyz'[Math.floor(Math.random() * 26)]; // Lowercase
    password += '0123456789'[Math.floor(Math.random() * 10)]; // Number
    password += '!@#$%^&*'[Math.floor(Math.random() * 8)]; // Special char
    
    // Fill the rest randomly
    for (let i = 4; i < length; i++) {
      password += charset[Math.floor(Math.random() * charset.length)];
    }
    
    // Shuffle the password
    return password.split('').sort(() => Math.random() - 0.5).join('');
  }
}
