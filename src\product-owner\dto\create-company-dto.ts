// src/product-owner/dto/create-company.dto.ts
import { IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator';

export class CreateCompanyDto {
  @IsNotEmpty()
  @IsString()
  name: string;

  @IsNotEmpty()
  @IsUUID()
  product_owner_id: string;

  @IsNotEmpty()
  @IsString()
  contact_person_firstName: string;

  @IsNotEmpty()
  @IsString()
  contact_person_lastName: string;

  @IsNotEmpty()
  @IsString()
  contact_person_email: string;

  @IsOptional()
  @IsString()
  contact_person_phone?: string;

  @IsNotEmpty()
  @IsString()
  address_line: string;

  @IsNotEmpty()
  @IsString()
  zipcode: string;

  @IsNotEmpty()
  @IsString()
  country: string;

  @IsNotEmpty()
  @IsString()
  state: string;

  @IsNotEmpty()
  @IsString()
  city: string;

  @IsOptional()
  @IsUUID()
  createdBy?: string;

  @IsOptional()
  @IsUUID()
  updatedBy?: string;

  @IsOptional()
  @IsUUID()
  deletedBy?: string;
}
