'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.createTable('Users', {
        id: {
          type: Sequelize.UUID,
          primaryKey: true,
          defaultValue: Sequelize.UUIDV4,
        },
        firstName: {
          type: Sequelize.STRING(150),
          allowNull: false,
        },
        lastName: {
          type: Sequelize.STRING(150),
          allowNull: false,
        },
        email: {
          type: Sequelize.STRING(150),
          allowNull: false,
          unique: true,
        },
        password: {
          type: Sequelize.STRING(255),
          allowNull: false,
        },
        roleId: {
          type: Sequelize.UUID,
          allowNull: false,
          references: {
            model: 'Roles',
            key: 'id',
          },
          onUpdate: 'CASCADE',
          onDelete: 'NO ACTION',
        },
        company_id: {
          type: Sequelize.UUID,
          allowNull: true,
          references: {
            model: 'Companies',
            key: 'id',
          },
          onUpdate: 'CASCADE',
          onDelete: 'NO ACTION',
        },
        supervisor_id: {
          type: Sequelize.UUID,
          allowNull: true,
          references: {
            model: 'Users',
            key: 'id',
          },
          onUpdate: 'CASCADE',
          onDelete: 'NO ACTION', // Fixed to avoid cascade path issue
        },
        department_id: {
          type: Sequelize.UUID,
          allowNull: true,
          references: {
            model: 'Departments',
            key: 'id',
          },
          onUpdate: 'CASCADE',
          onDelete: 'NO ACTION',
        },
        isActive: {
          type: Sequelize.BOOLEAN,
          defaultValue: true,
        },
        isDeleted: {
          type: Sequelize.BOOLEAN,
          defaultValue: false,
        },
        isTemporaryPassword: {
          type: Sequelize.BOOLEAN,
          defaultValue: false,
        },
        createdAt: {
          type: Sequelize.DATE,
          defaultValue: Sequelize.fn('GETDATE'),
        },
        createdBy: {
          type: Sequelize.UUID,
        },
        updatedAt: {
          type: Sequelize.DATE,
          defaultValue: Sequelize.fn('GETDATE'),
        },
        updatedBy: {
          type: Sequelize.UUID,
        },
        deletedBy: {
          type: Sequelize.UUID,
        },
        refreshToken: {
          type: Sequelize.STRING,
          allowNull: true,
        },
      });
    } catch (error) {
      console.error('Error in user migration:', error);
      throw error; // Recommended: rethrow so migration fails visibly
    }
  },

  down: async (queryInterface) => {
    await queryInterface.dropTable('Users');
  },
};
