#!/usr/bin/env node

/**
 * Complete Database Setup for Employee Survey System
 * Runs migrations and seeders in proper order
 */

const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Employee Survey System - Complete Database Setup');
console.log('==================================================');

// Configuration
const config = {
  skipMigrations: process.argv.includes('--skip-migrations'),
  skipSeeders: process.argv.includes('--skip-seeders'),
  resetDatabase: process.argv.includes('--reset'),
  verbose: process.argv.includes('--verbose')
};

// Helper function to run scripts
function runScript(scriptPath, description) {
  return new Promise((resolve, reject) => {
    console.log(`\n🔄 ${description}...`);
    console.log('─'.repeat(50));
    
    const child = spawn('node', [scriptPath], {
      stdio: 'inherit',
      cwd: process.cwd()
    });
    
    child.on('close', (code) => {
      if (code === 0) {
        console.log(`✅ ${description} completed successfully`);
        resolve();
      } else {
        console.error(`❌ ${description} failed with code ${code}`);
        reject(new Error(`${description} failed`));
      }
    });
    
    child.on('error', (error) => {
      console.error(`❌ Error running ${description}:`, error.message);
      reject(error);
    });
  });
}

// Check database connection
async function checkDatabaseConnection() {
  try {
    console.log('🔍 Checking database connection...');
    
    // Try to connect to database
    execSync('npx sequelize-cli db:migrate:status', {
      stdio: config.verbose ? 'inherit' : 'pipe',
      cwd: process.cwd()
    });
    
    console.log('✅ Database connection successful');
    return true;
  } catch (error) {
    console.log('⚠️  Database connection failed or not initialized');
    return false;
  }
}

// Reset database if requested
async function resetDatabase() {
  if (!config.resetDatabase) return;
  
  try {
    console.log('🗑️  Resetting database...');
    
    // Undo all migrations
    execSync('npx sequelize-cli db:migrate:undo:all', {
      stdio: config.verbose ? 'inherit' : 'pipe',
      cwd: process.cwd()
    });
    
    // Undo all seeders
    execSync('npx sequelize-cli db:seed:undo:all', {
      stdio: config.verbose ? 'inherit' : 'pipe',
      cwd: process.cwd()
    });
    
    console.log('✅ Database reset completed');
  } catch (error) {
    console.log('⚠️  Database reset failed (might be empty already)');
  }
}

// Main setup function
async function setupDatabase() {
  console.log('📋 Setup Configuration:');
  console.log(`   Skip Migrations: ${config.skipMigrations}`);
  console.log(`   Skip Seeders: ${config.skipSeeders}`);
  console.log(`   Reset Database: ${config.resetDatabase}`);
  console.log(`   Verbose Output: ${config.verbose}`);
  
  try {
    // Step 1: Check database connection
    const dbConnected = await checkDatabaseConnection();
    if (!dbConnected && !config.resetDatabase) {
      console.log('💡 Tip: Use --reset flag to initialize a fresh database');
    }
    
    // Step 2: Reset database if requested
    await resetDatabase();
    
    // Step 3: Run migrations
    if (!config.skipMigrations) {
      await runScript(
        path.join(__dirname, 'run-migrations-sequentially.js'),
        'Running Migrations'
      );
    } else {
      console.log('⏭️  Skipping migrations (--skip-migrations flag)');
    }
    
    // Step 4: Run seeders
    if (!config.skipSeeders) {
      await runScript(
        path.join(__dirname, 'run-seeders-sequentially.js'),
        'Running Seeders'
      );
    } else {
      console.log('⏭️  Skipping seeders (--skip-seeders flag)');
    }
    
    // Step 5: Final verification
    console.log('\n🔍 Final Verification...');
    console.log('─'.repeat(50));
    
    try {
      console.log('📊 Migration Status:');
      execSync('npx sequelize-cli db:migrate:status', {
        stdio: 'inherit',
        cwd: process.cwd()
      });
      
      console.log('\n🌱 Seeder Status:');
      execSync('npx sequelize-cli db:seed:status', {
        stdio: 'inherit',
        cwd: process.cwd()
      });
    } catch (error) {
      console.log('⚠️  Could not verify status');
    }
    
    // Success message
    console.log('\n🎉 Database Setup Complete!');
    console.log('===========================');
    console.log('✅ Your Employee Survey System database is ready!');
    console.log('');
    console.log('📋 What was set up:');
    console.log('   • Core tables (Users, Companies, Departments)');
    console.log('   • Question system with translations');
    console.log('   • Survey response tracking');
    console.log('   • Sample data for testing');
    console.log('');
    console.log('🚀 Next Steps:');
    console.log('   1. Start your application: npm start');
    console.log('   2. Test API endpoints');
    console.log('   3. Create your first survey!');
    console.log('');
    console.log('📚 Useful Commands:');
    console.log('   • Check status: npm run db:status');
    console.log('   • Reset database: npm run db:reset');
    console.log('   • Add new migration: npx sequelize-cli migration:generate --name your-migration');
    
  } catch (error) {
    console.error('\n❌ Database setup failed:', error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('   1. Check your database connection in config/config.json');
    console.log('   2. Ensure your database server is running');
    console.log('   3. Verify database permissions');
    console.log('   4. Try running with --reset flag for fresh setup');
    console.log('   5. Run with --verbose flag for detailed output');
    
    process.exit(1);
  }
}

// Show help
function showHelp() {
  console.log('🔧 Database Setup Script Usage:');
  console.log('===============================');
  console.log('');
  console.log('Basic usage:');
  console.log('  node scripts/setup-database.js');
  console.log('');
  console.log('Options:');
  console.log('  --reset              Reset database before setup');
  console.log('  --skip-migrations    Skip running migrations');
  console.log('  --skip-seeders       Skip running seeders');
  console.log('  --verbose            Show detailed output');
  console.log('  --help               Show this help message');
  console.log('');
  console.log('Examples:');
  console.log('  node scripts/setup-database.js --reset');
  console.log('  node scripts/setup-database.js --skip-seeders');
  console.log('  node scripts/setup-database.js --reset --verbose');
}

// Handle help flag
if (process.argv.includes('--help')) {
  showHelp();
  process.exit(0);
}

// Error handling
process.on('unhandledRejection', (error) => {
  console.error('❌ Unhandled error:', error);
  process.exit(1);
});

// Run the setup
setupDatabase();
