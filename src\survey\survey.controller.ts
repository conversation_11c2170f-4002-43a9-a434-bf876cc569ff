import { 
  Controller, 
  Post, 
  Get, 
  Delete,
  Body, 
  Param, 
  Query,
  UseGuards,
  ParseUUI<PERSON>ipe,
  BadRequestException
} from '@nestjs/common';
import { SurveyService, SurveySubmissionDto } from './survey.service';
import { JwtAuthGuard } from 'src/auth/guards/jwt-auth-guards';

@Controller('api/survey')
@UseGuards(JwtAuthGuard)
export class SurveyController {
  constructor(private readonly surveyService: SurveyService) {}

  /**
   * Submit a complete survey
   * POST /api/survey/submit
   */
  @Post('submit')
  async submitSurvey(@Body() submissionData: SurveySubmissionDto) {
    try {
      return await this.surveyService.submitSurvey(submissionData);
    } catch (error) {
      throw new BadRequestException(`Failed to submit survey: ${error.message}`);
    }
  }

  /**
   * Get user's survey history
   * GET /api/survey/user/:userId/history
   */
  @Get('user/:userId/history')
  async getUserSurveyHistory(
    @Param('userId', ParseUUIDPipe) userId: string,
    @Query('limit') limit?: string
  ) {
    try {
      const limitNum = limit ? parseInt(limit) : 10;
      return await this.surveyService.getUserSurveyHistory(userId, limitNum);
    } catch (error) {
      throw new BadRequestException(`Failed to get survey history: ${error.message}`);
    }
  }

  /**
   * Get survey session details
   * GET /api/survey/session/:sessionId
   */
  @Get('session/:sessionId')
  async getSurveySessionDetails(@Param('sessionId', ParseUUIDPipe) sessionId: string) {
    try {
      return await this.surveyService.getSurveySessionDetails(sessionId);
    } catch (error) {
      throw new BadRequestException(`Failed to get session details: ${error.message}`);
    }
  }

  /**
   * Check if user has submitted survey in date range
   * GET /api/survey/user/:userId/check-submission
   */
  @Get('user/:userId/check-submission')
  async checkUserSurveySubmission(
    @Param('userId', ParseUUIDPipe) userId: string,
    @Query('companyId', ParseUUIDPipe) companyId: string,
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string
  ) {
    try {
      const start = new Date(startDate);
      const end = new Date(endDate);

      if (isNaN(start.getTime()) || isNaN(end.getTime())) {
        throw new BadRequestException('Invalid date format');
      }

      const hasSubmitted = await this.surveyService.hasUserSubmittedSurvey(
        userId,
        companyId,
        start,
        end
      );

      return { hasSubmitted };
    } catch (error) {
      throw new BadRequestException(`Failed to check submission: ${error.message}`);
    }
  }

  /**
   * Check if user can submit survey today
   * GET /api/survey/user/:userId/can-submit-today
   */
  @Get('user/:userId/can-submit-today')
  async checkUserCanSubmitToday(
    @Param('userId', ParseUUIDPipe) userId: string,
    @Query('companyId', ParseUUIDPipe) companyId: string
  ) {
    try {
      const hasSubmittedToday = await this.surveyService.hasUserSubmittedSurveyToday(userId, companyId);

      return {
        canSubmit: !hasSubmittedToday,
        hasSubmittedToday,
        message: hasSubmittedToday
          ? 'You have already submitted a survey today. Only one survey submission per day is allowed.'
          : 'You can submit a survey today.'
      };
    } catch (error) {
      throw new BadRequestException(`Failed to check daily submission: ${error.message}`);
    }
  }

  /**
   * Get company survey completion rate
   * GET /api/survey/company/:companyId/completion-rate
   */
  @Get('company/:companyId/completion-rate')
  async getCompanyCompletionRate(
    @Param('companyId', ParseUUIDPipe) companyId: string,
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string
  ) {
    try {
      const start = new Date(startDate);
      const end = new Date(endDate);
      
      if (isNaN(start.getTime()) || isNaN(end.getTime())) {
        throw new BadRequestException('Invalid date format');
      }

      return await this.surveyService.getCompanySurveyCompletionRate(companyId, start, end);
    } catch (error) {
      throw new BadRequestException(`Failed to get completion rate: ${error.message}`);
    }
  }

  /**
   * Delete survey session
   * DELETE /api/survey/session/:sessionId
   */
  @Delete('session/:sessionId')
  async deleteSurveySession(
    @Param('sessionId', ParseUUIDPipe) sessionId: string,
    @Body('deletedBy') deletedBy: string
  ) {
    try {
      const result = await this.surveyService.deleteSurveySession(sessionId, deletedBy);
      if (!result) {
        throw new BadRequestException('Survey session not found or already deleted');
      }
      return { message: 'Survey session deleted successfully' };
    } catch (error) {
      throw new BadRequestException(`Failed to delete survey session: ${error.message}`);
    }
  }
}
