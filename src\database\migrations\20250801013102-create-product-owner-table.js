'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.createTable('ProductOwners', {
        id: {
          type: Sequelize.UUID,
          primaryKey: true,
          defaultValue: Sequelize.UUIDV4,
        },
        firstName: {
          type: Sequelize.STRING,
          allowNull: false,
        },
        lastName: {
          type: Sequelize.STRING,
          allowNull: false,
        },
        email: {
          type: Sequelize.STRING,
          allowNull: false,
          unique: true,
        },
        password: {
          type: Sequelize.STRING,
          allowNull: false,
        },
        roleId: {
          type: Sequelize.UUID,
          allowNull: false,
          references: {
            model: 'Roles', // adjust if your table name is different
            key: 'id',
          },
        },
        isActive: {
          type: Sequelize.BOOLEAN,
          defaultValue: true,
        },
        isDeleted: {
          type: Sequelize.BOOLEAN,
          defaultValue: false,
        },
        createdAt: {
          type: Sequelize.DATE,
          defaultValue: Sequelize.fn('GETDATE'),
        },
        updatedAt: {
          type: Sequelize.DATE,
          defaultValue: Sequelize.fn('GETDATE'),
        },
        refreshToken: {
          type: Sequelize.STRING,
          allowNull: true,
        },
      });
    } catch (error) {
      console.log("error in product owner migration is", error);
    }
  },

  down: async (queryInterface) => {
    await queryInterface.dropTable('ProductOwners');
  },
};
