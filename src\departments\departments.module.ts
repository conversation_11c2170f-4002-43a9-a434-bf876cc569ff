import { Modu<PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { DepartmentsController } from './departments.controller';
import { DepartmentsService } from './departments.service';
import { Department } from '../database/models/department.model';
import { Company } from '../database/models/company.model';
import { User } from '../database/models/user.model';
import { UserDepartment } from '../database/models/user-department.model';

@Module({
  imports: [
    SequelizeModule.forFeature([
      Department,
      Company,
      User,
      UserDepartment
    ])
  ],
  controllers: [DepartmentsController],
  providers: [DepartmentsService],
  exports: [DepartmentsService]
})
export class DepartmentsModule {}
