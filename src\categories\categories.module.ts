import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { CategoriesController } from './categories.controller';
import { CategoriesService } from './categories.service';
import { AnswerCategory } from '../database/models/answer-category.model';
import { QuestionOption } from '../database/models/question-option.model';
import { SurveyResponse } from '../database/models/survey-response.model';

@Module({
  imports: [
    SequelizeModule.forFeature([
      AnswerCategory,
      QuestionOption,
      SurveyResponse
    ])
  ],
  controllers: [CategoriesController],
  providers: [CategoriesService],
  exports: [CategoriesService]
})
export class CategoriesModule {}
