import { Table, Column, Model, DataType, Foreign<PERSON>ey, BelongsTo, De<PERSON><PERSON>, PrimaryKey, AllowNull, CreatedAt, UpdatedAt } from 'sequelize-typescript';
import { QuestionOption } from './question-option.model';
import { Language } from './language.model';

@Table({
  tableName: 'QuestionOptionTranslations',
  timestamps: true
})
export class QuestionOptionTranslation extends Model<QuestionOptionTranslation> {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column({ type: DataType.UUID })
  declare id: string;

  @ForeignKey(() => QuestionOption)
  @AllowNull(false)
  @Column(DataType.UUID)
  declare question_option_id: string;

  @ForeignKey(() => Language)
  @AllowNull(false)
  @Column(DataType.UUID)
  declare language_id: string;

  @AllowNull(false)
  @Column({ type: DataType.STRING(255) })
  declare option_text: string;

  @Default(true)
  @Column(DataType.BOOLEAN)
  declare isActive: boolean;

  @CreatedAt
  @Column(DataType.DATE)
  declare createdAt: Date;

  @Column(DataType.UUID)
  declare createdBy: string;

  @UpdatedAt
  @Column(DataType.DATE)
  declare updatedAt: Date;

  @Column(DataType.UUID)
  declare updatedBy: string;

  @Default(false)
  @Column(DataType.BOOLEAN)
  declare isDeleted: boolean;

  @Column(DataType.UUID)
  declare deletedBy: string;

  // Associations
  @BelongsTo(() => QuestionOption)
  questionOption: QuestionOption;

  @BelongsTo(() => Language)
  language: Language;
}
