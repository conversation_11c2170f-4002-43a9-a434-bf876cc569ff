const { Sequelize } = require('sequelize');

// Database configuration (adjust as needed)
const sequelize = new Sequelize({
  dialect: 'mssql',
  host: 'localhost',
  port: 1433,
  username: "pratik_1101",
  password: 'pratik#123',
  database: "test-db",
  dialectOptions: {
    options: {
      encrypt: true,
      trustServerCertificate: true,
    },
    instanceName: 'SQLEXPRESS',
  },
  logging: console.log
});

async function fixMigrationStatus() {
  try {
    console.log('🔧 Fixing migration status...');
    
    // Check if SequelizeMeta table exists
    const metaTableExists = await sequelize.query(`
      SELECT COUNT(*) as count 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_NAME = 'SequelizeMeta'
    `, { type: Sequelize.QueryTypes.SELECT });
    
    if (metaTableExists[0].count === 0) {
      console.log('❌ SequelizeMeta table does not exist. Please run sequelize-cli db:migrate first.');
      return;
    }
    
    // Check if the migration is already marked as completed
    const migrationExists = await sequelize.query(`
      SELECT COUNT(*) as count 
      FROM SequelizeMeta 
      WHERE name = 'add-category-id-to-question-options.js'
    `, { type: Sequelize.QueryTypes.SELECT });
    
    if (migrationExists[0].count > 0) {
      console.log('✅ Migration is already marked as completed');
      return;
    }
    
    // Since the column already exists, mark the migration as completed
    await sequelize.query(`
      INSERT INTO SequelizeMeta (name) 
      VALUES ('add-category-id-to-question-options.js')
    `);
    
    console.log('✅ Migration marked as completed');
    
    // Now check if we need to populate categories for existing question options
    const optionsNeedingCategories = await sequelize.query(`
      SELECT COUNT(*) as count 
      FROM QuestionOptions 
      WHERE category_id IS NULL AND isDeleted = 0
    `, { type: Sequelize.QueryTypes.SELECT });
    
    if (optionsNeedingCategories[0].count > 0) {
      console.log(`🔧 Found ${optionsNeedingCategories[0].count} question options that need categories`);
      
      // Apply the same mapping logic as in the migration
      const categoryMappings = [
        { patterns: ['excellent', 'outstanding', 'strongly agree', 'very satisfied', 'love it', 'amazing'], categoryId: '11111111-1111-1111-1111-111111111111' },
        { patterns: ['good', 'agree', 'satisfied', 'like it', 'yes', 'positive', 'happy'], categoryId: '22222222-2222-2222-2222-222222222222' },
        { patterns: ['okay', 'ok', 'neutral', 'neither', 'average', 'maybe', 'unsure'], categoryId: '33333333-3333-3333-3333-333333333333' },
        { patterns: ['bad', 'disagree', 'unsatisfied', 'dislike', 'no', 'negative', 'unhappy'], categoryId: '44444444-4444-4444-4444-444444444444' },
        { patterns: ['terrible', 'awful', 'strongly disagree', 'very unsatisfied', 'hate it', 'horrible'], categoryId: '55555555-5555-5555-5555-555555555555' }
      ];

      for (const mapping of categoryMappings) {
        for (const pattern of mapping.patterns) {
          await sequelize.query(`
            UPDATE QuestionOptions 
            SET category_id = '${mapping.categoryId}' 
            WHERE LOWER(option_text) LIKE '%${pattern}%' 
            AND category_id IS NULL
          `);
        }
      }

      // Set remaining unmapped options to NEUTRAL as default
      await sequelize.query(`
        UPDATE QuestionOptions 
        SET category_id = '33333333-3333-3333-3333-333333333333'
        WHERE category_id IS NULL
      `);
      
      console.log('✅ Question options updated with categories');
    } else {
      console.log('✅ All question options already have categories or no options exist');
    }
    
  } catch (error) {
    console.error('❌ Error fixing migration status:', error.message);
  } finally {
    await sequelize.close();
  }
}

fixMigrationStatus();
