import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/sequelize';
import { QuestionService } from './question.service';
import { Question } from '../database/models/question.model';
import { QuestionOption } from '../database/models/question-option.model';
import { CompanyQuestionMap } from '../database/models/company-question-map.model';
import { Company } from '../database/models/company.model';
import { Sequelize } from 'sequelize-typescript';
import { TranslationService } from '../common/services/translation.service';

describe('QuestionService - getAllQuestions with Pagination', () => {
  let service: QuestionService;
  let questionModel: typeof Question;

  const mockQuestions = [
    {
      id: 'question-1',
      question_text: 'How satisfied are you?',
      isActive: true,
      isDeleted: false,
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: 'user-1',
      updatedBy: 'user-1',
      options: [
        {
          id: 'option-1',
          question_id: 'question-1',
          option_text: 'Very satisfied',
          option_value: 5,
          category_id: 'cat-1',
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date(),
        }
      ]
    },
    {
      id: 'question-2',
      question_text: 'How likely are you to recommend?',
      isActive: true,
      isDeleted: false,
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: 'user-1',
      updatedBy: 'user-1',
      options: []
    }
  ];

  const mockQuestionModel = {
    findAndCountAll: jest.fn(),
  };

  const mockTranslationService = {
    getTranslations: jest.fn().mockResolvedValue([]),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        QuestionService,
        {
          provide: getModelToken(Question),
          useValue: mockQuestionModel,
        },
        {
          provide: getModelToken(QuestionOption),
          useValue: {},
        },
        {
          provide: getModelToken(CompanyQuestionMap),
          useValue: {},
        },
        {
          provide: getModelToken(Company),
          useValue: {},
        },
        {
          provide: Sequelize,
          useValue: {},
        },
        {
          provide: TranslationService,
          useValue: mockTranslationService,
        },
      ],
    }).compile();

    service = module.get<QuestionService>(QuestionService);
    questionModel = module.get<typeof Question>(getModelToken(Question));
  });

  it('should return paginated questions with default pagination', async () => {
    // Mock the database response
    mockQuestionModel.findAndCountAll.mockResolvedValue({
      rows: mockQuestions,
      count: 2,
    });

    const result = await service.getAllQuestions('en');

    expect(result).toEqual({
      questions: expect.arrayContaining([
        expect.objectContaining({
          id: 'question-1',
          question_text: 'How satisfied are you?',
          isActive: true,
        }),
        expect.objectContaining({
          id: 'question-2',
          question_text: 'How likely are you to recommend?',
          isActive: true,
        }),
      ]),
      pagination: {
        total: 2,
        page: 1,
        limit: 10,
        totalPages: 1,
      },
    });

    expect(mockQuestionModel.findAndCountAll).toHaveBeenCalledWith({
      where: { isDeleted: false },
      include: [
        {
          model: QuestionOption,
          where: { isDeleted: false },
          required: false,
        },
      ],
      limit: 10,
      offset: 0,
      order: [['createdAt', 'DESC']],
    });
  });

  it('should return paginated questions with custom pagination', async () => {
    mockQuestionModel.findAndCountAll.mockResolvedValue({
      rows: [mockQuestions[0]],
      count: 2,
    });

    const result = await service.getAllQuestions('en', {
      page: 2,
      limit: 1,
      isActive: true,
    });

    expect(result.pagination).toEqual({
      total: 2,
      page: 2,
      limit: 1,
      totalPages: 2,
    });

    expect(mockQuestionModel.findAndCountAll).toHaveBeenCalledWith({
      where: { isDeleted: false, isActive: true },
      include: [
        {
          model: QuestionOption,
          where: { isDeleted: false },
          required: false,
        },
      ],
      limit: 1,
      offset: 1,
      order: [['createdAt', 'DESC']],
    });
  });
});
