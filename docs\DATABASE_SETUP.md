# 🗄️ Database Setup Guide - Employee Survey System

This guide explains how to set up and manage the database for the Employee Survey System with proper sequential migration and seeding.

## 🚀 Quick Start

### **Complete Setup (Recommended)**
```bash
# Set up everything from scratch
npm run db:setup:fresh

# Or if database already exists
npm run db:setup
```

### **Step-by-Step Setup**
```bash
# 1. Run migrations in proper order
npm run db:migrate

# 2. Run seeders in proper order  
npm run db:seed

# 3. Check status
npm run db:status
```

## 📋 Available Commands

### **Complete Database Management**
| Command | Description |
|---------|-------------|
| `npm run db:setup` | Complete setup (migrations + seeders) |
| `npm run db:setup:fresh` | Fresh setup (reset + migrations + seeders) |
| `npm run db:reset` | Reset and rebuild database |
| `npm run db:status` | Check migration and seeder status |

### **Migration Management**
| Command | Description |
|---------|-------------|
| `npm run db:migrate` | Run migrations sequentially |
| `npm run db:migrate:status` | Check migration status |
| `npm run db:migrate:undo` | Undo last migration |
| `npm run db:migrate:reset` | Reset all migrations and re-run |

### **Seeder Management**
| Command | Description |
|---------|-------------|
| `npm run db:seed` | Run seeders sequentially |
| `npm run db:seed:all` | Run all seeders (Sequelize default) |
| `npm run db:seed:status` | Check seeder status |
| `npm run db:seed:undo` | Undo all seeders |
| `npm run survey:seed` | Run employee survey questions seeder |

## 🔄 Migration Order

The system runs migrations in the following dependency order:

### **Phase 1: Foundation** 
- `create-roles-table.js`
- `create-languages-table.js`
- `create-answer-categories-table.js`

### **Phase 2: User Management**
- `create-product-owner-table.js`
- `create-users-table.js`

### **Phase 3: Company Structure**
- `create-company-table.js`
- `create-departments-table.js`

### **Phase 4: Question System**
- `create-questions-table.js`
- `create-question-options-table.js`
- `create-question-translations-table.js`
- `create-question-option-translations-table.js`

### **Phase 5: Company-Question Mapping**
- `create-company-question-map-table.js`

### **Phase 6: Survey System**
- `create-survey-responses-table.js`
- `create-reports-table.js`

### **Phase 7: Schema Updates**
- `add-category-id-to-question-options.js`
- `add-department-id-to-users.js`
- `add-refresh-token-to-product-owners.js`
- `add-temporary-password-field.js`

### **Phase 8: Data Migration**
- `migrate-existing-texts-to-translations.js`

## 🌱 Seeder Order

Seeders run in dependency order:

### **Phase 1: Foundation Data**
- `create-roles.js`
- `create-languages.js`
- `create-answer-categories.js`

### **Phase 2: User Management**
- `create-product-owner.js`

### **Phase 3: Company Structure**
- `create-companies.js`
- `create-departments.js`

### **Phase 4: Survey Questions**
- `20250726-employee-survey-questions.js`

### **Phase 5: Sample Data**
- `create-sample-users.js`
- `create-sample-responses.js`

## 🛠️ Advanced Usage

### **Custom Script Options**
```bash
# Setup with options
node scripts/setup-database.js --reset --verbose
node scripts/setup-database.js --skip-seeders
node scripts/setup-database.js --skip-migrations

# Individual scripts
node scripts/run-migrations-sequentially.js
node scripts/run-seeders-sequentially.js
```

### **Manual Sequelize Commands**
```bash
# Traditional Sequelize CLI (not recommended for this project)
npx sequelize-cli db:migrate
npx sequelize-cli db:seed:all

# Check status
npx sequelize-cli db:migrate:status
npx sequelize-cli db:seed:status
```

## 🔧 Troubleshooting

### **Common Issues**

#### **1. English Language Not Found**
```bash
# Solution: Run languages seeder first
npx sequelize-cli db:seed --seed *languages*
```

#### **2. Category Not Found**
```bash
# Solution: Run categories seeder first
npx sequelize-cli db:seed --seed *categories*
```

#### **3. Migration Order Issues**
```bash
# Solution: Use our sequential migration runner
npm run db:migrate
```

#### **4. Database Connection Issues**
```bash
# Check your config/config.json
# Ensure database server is running
# Verify connection credentials
```

### **Reset Everything**
```bash
# Nuclear option - start completely fresh
npm run db:setup:fresh
```

## 📊 Database Schema Overview

### **Core Tables**
- **Users** - Employee accounts
- **Companies** - Client organizations  
- **Departments** - Company departments
- **Roles** - User permission levels

### **Question System**
- **Questions** - Survey questions
- **QuestionOptions** - Answer choices
- **QuestionTranslations** - Multi-language support
- **QuestionOptionTranslations** - Multi-language options

### **Survey System**
- **SurveyResponses** - Employee answers
- **CompanyQuestionMaps** - Company-specific questions
- **Reports** - Analytics and reporting

### **Support Tables**
- **Languages** - Supported languages
- **AnswerCategories** - Response categorization

## 🎯 Best Practices

1. **Always use the sequential runners** instead of direct Sequelize CLI
2. **Run migrations before seeders**
3. **Check status before making changes**
4. **Use fresh setup for development**
5. **Backup production before migrations**

## 📚 Next Steps

After successful database setup:

1. **Start the application**: `npm start`
2. **Test API endpoints**
3. **Create your first survey**
4. **Assign questions to companies**

## 🔗 Related Documentation

- [API Documentation](./API.md)
- [Survey System Guide](./SURVEY_GUIDE.md)
- [Deployment Guide](./DEPLOYMENT.md)
