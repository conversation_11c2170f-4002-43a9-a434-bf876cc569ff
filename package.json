{"name": "employee-survey-backend", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "db:setup": "node scripts/setup-database.js", "db:setup:fresh": "node scripts/setup-database.js --reset", "db:migrate": "node scripts/run-migrations-sequentially.js", "db:migrate:status": "npx sequelize-cli db:migrate:status", "db:migrate:undo": "npx sequelize-cli db:migrate:undo", "db:migrate:reset": "npx sequelize-cli db:migrate:undo:all && npm run db:migrate", "db:seed": "node scripts/run-seeders-sequentially.js", "db:seed:all": "npx sequelize-cli db:seed:all", "db:seed:undo": "npx sequelize-cli db:seed:undo:all", "db:seed:status": "npx sequelize-cli db:seed:status", "db:reset": "npm run db:migrate:reset && npm run db:seed", "db:status": "npm run db:migrate:status && npm run db:seed:status", "survey:seed": "node scripts/seed-survey-questions.js", "generate:migrations-from-models": "node scripts/generate-migrations-from-models.js"}, "dependencies": {"@nestjs/common": "^11.0.1", "@nestjs/core": "^11.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.1", "@nestjs/sequelize": "^11.0.0", "bcrypt": "^6.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "passport-jwt": "^4.0.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "sequelize": "^6.37.7", "sequelize-auto-migrations": "^1.0.3", "sequelize-cli": "^6.6.3", "sequelize-typescript": "^2.1.6", "tedious": "^18.6.1"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/node": "^22.10.7", "@types/passport-jwt": "^4.0.1", "@types/supertest": "^6.0.2", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "jest": "^29.7.0", "prettier": "^3.4.2", "sequelize-mig": "^3.1.3", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}