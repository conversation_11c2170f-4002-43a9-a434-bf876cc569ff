# 📧 Contact API - cURL Examples

Complete cURL examples for testing the Contact Form API system.

## 🌐 Public Endpoints (Domain Restricted)

### **Submit Contact Form**
```bash
# Basic contact form submission
curl -X POST http://localhost:3000/contact/submit \
  -H "Content-Type: application/json" \
  -H "Origin: http://localhost:3000" \
  -H "User-Agent: Mozilla/5.0 (Contact Form Test)" \
  -d '{
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "phone": "******-123-4567",
    "subject": "Inquiry about Employee Survey System",
    "message": "Hi, I am interested in learning more about your employee survey platform. Could you please provide more information about pricing and features? Thank you!"
  }'
```

### **Submit from Different Domain (Should Fail)**
```bash
# This should be rejected due to domain restriction
curl -X POST http://localhost:3000/contact/submit \
  -H "Content-Type: application/json" \
  -H "Origin: http://unauthorized-domain.com" \
  -d '{
    "firstName": "Test",
    "lastName": "User",
    "email": "<EMAIL>",
    "subject": "Test from unauthorized domain",
    "message": "This should be blocked by domain restriction."
  }'
```

### **Health Check**
```bash
# Check if contact service is healthy
curl -X GET http://localhost:3000/contact/health \
  -H "Origin: http://localhost:3000"
```

## 🔐 Admin Endpoints (Authentication Required)

### **Get All Contact Submissions**
```bash
# Get all submissions (paginated)
curl -X GET "http://localhost:3000/contact/submissions?page=1&limit=10" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# Get submissions with specific status
curl -X GET "http://localhost:3000/contact/submissions?status=new&page=1&limit=20" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# Include spam submissions
curl -X GET "http://localhost:3000/contact/submissions?includeSpam=true" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### **Get Submission Statistics**
```bash
# Get overview statistics
curl -X GET http://localhost:3000/contact/submissions/stats \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### **Get Specific Submission**
```bash
# Get submission by ID
curl -X GET http://localhost:3000/contact/submissions/SUBMISSION_UUID \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### **Update Submission Status**
```bash
# Mark as read
curl -X PUT http://localhost:3000/contact/submissions/SUBMISSION_UUID/status \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "status": "read"
  }'

# Mark as replied
curl -X PUT http://localhost:3000/contact/submissions/SUBMISSION_UUID/status \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "status": "replied"
  }'

# Mark as closed
curl -X PUT http://localhost:3000/contact/submissions/SUBMISSION_UUID/status \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "status": "closed"
  }'
```

### **Mark Submission as Spam**
```bash
# Mark submission as spam
curl -X PUT http://localhost:3000/contact/submissions/SUBMISSION_UUID/spam \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### **Delete Submission**
```bash
# Soft delete submission
curl -X DELETE http://localhost:3000/contact/submissions/SUBMISSION_UUID \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## 🧪 Testing Scenarios

### **Valid Submission Test**
```bash
# Test with all required fields
curl -X POST http://localhost:3000/contact/submit \
  -H "Content-Type: application/json" \
  -H "Origin: http://localhost:3000" \
  -H "Referer: http://localhost:3000/contact" \
  -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36" \
  -d '{
    "firstName": "Alice",
    "lastName": "Johnson",
    "email": "<EMAIL>",
    "phone": "******-987-6543",
    "subject": "Partnership Opportunity",
    "message": "Hello, we are interested in partnering with your company for our employee engagement initiatives. We have over 500 employees and would like to discuss how your survey platform could help us improve our workplace culture. Please let us know the best time to schedule a call."
  }'
```

### **Minimal Required Fields**
```bash
# Test with only required fields
curl -X POST http://localhost:3000/contact/submit \
  -H "Content-Type: application/json" \
  -H "Origin: http://localhost:3000" \
  -d '{
    "firstName": "Bob",
    "lastName": "Smith",
    "email": "<EMAIL>",
    "subject": "Quick Question",
    "message": "What are your business hours?"
  }'
```

### **Validation Error Tests**
```bash
# Missing required field (should fail)
curl -X POST http://localhost:3000/contact/submit \
  -H "Content-Type: application/json" \
  -H "Origin: http://localhost:3000" \
  -d '{
    "firstName": "Test",
    "email": "<EMAIL>",
    "subject": "Missing last name",
    "message": "This should fail validation."
  }'

# Invalid email format (should fail)
curl -X POST http://localhost:3000/contact/submit \
  -H "Content-Type: application/json" \
  -H "Origin: http://localhost:3000" \
  -d '{
    "firstName": "Test",
    "lastName": "User",
    "email": "invalid-email",
    "subject": "Invalid email test",
    "message": "This should fail due to invalid email."
  }'

# Message too short (should fail)
curl -X POST http://localhost:3000/contact/submit \
  -H "Content-Type: application/json" \
  -H "Origin: http://localhost:3000" \
  -d '{
    "firstName": "Test",
    "lastName": "User",
    "email": "<EMAIL>",
    "subject": "Short message test",
    "message": "Too short"
  }'
```

### **Spam Detection Tests**
```bash
# Test with spam keywords (should be flagged)
curl -X POST http://localhost:3000/contact/submit \
  -H "Content-Type: application/json" \
  -H "Origin: http://localhost:3000" \
  -d '{
    "firstName": "Spam",
    "lastName": "Bot",
    "email": "<EMAIL>",
    "subject": "Congratulations! You won the lottery!",
    "message": "Click here to claim your free money! Visit our casino and win big with viagra offers!"
  }'

# Test with excessive links (should be flagged)
curl -X POST http://localhost:3000/contact/submit \
  -H "Content-Type: application/json" \
  -H "Origin: http://localhost:3000" \
  -d '{
    "firstName": "Link",
    "lastName": "Spammer",
    "email": "<EMAIL>",
    "subject": "Check out these links",
    "message": "Visit https://site1.com and https://site2.com and https://site3.com and https://site4.com for amazing deals!"
  }'
```

## 🔧 Environment-Specific Examples

### **Development Environment**
```bash
# Local development with common ports
curl -X POST http://localhost:3000/contact/submit \
  -H "Content-Type: application/json" \
  -H "Origin: http://localhost:4200" \
  -d '{"firstName":"Dev","lastName":"User","email":"dev@localhost","subject":"Dev Test","message":"Testing from Angular dev server"}'

curl -X POST http://localhost:3000/contact/submit \
  -H "Content-Type: application/json" \
  -H "Origin: http://127.0.0.1:3001" \
  -d '{"firstName":"Local","lastName":"Test","email":"test@127.0.0.1","subject":"Local Test","message":"Testing from local IP"}'
```

### **Production Environment**
```bash
# Production domain (update with your actual domain)
curl -X POST https://api.yourdomain.com/contact/submit \
  -H "Content-Type: application/json" \
  -H "Origin: https://www.yourdomain.com" \
  -H "Referer: https://www.yourdomain.com/contact" \
  -d '{
    "firstName": "Production",
    "lastName": "User",
    "email": "<EMAIL>",
    "subject": "Production Contact",
    "message": "This is a real contact form submission from production."
  }'
```

## 📊 Response Examples

### **Successful Submission**
```json
{
  "success": true,
  "message": "Thank you for your message! We will get back to you soon.",
  "data": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "phone": "******-123-4567",
    "subject": "Inquiry about Employee Survey System",
    "message": "Hi, I am interested in learning more...",
    "status": "new",
    "createdAt": "2025-01-27T10:30:00.000Z",
    "updatedAt": "2025-01-27T10:30:00.000Z"
  }
}
```

### **Domain Restriction Error**
```json
{
  "statusCode": 403,
  "message": "Access denied. Domain 'unauthorized-domain.com' is not authorized to access this resource.",
  "error": "Forbidden"
}
```

### **Validation Error**
```json
{
  "statusCode": 400,
  "message": [
    "First name is required",
    "Please provide a valid email address",
    "Message must be at least 10 characters long"
  ],
  "error": "Bad Request"
}
```

### **Submission Statistics**
```json
{
  "success": true,
  "data": {
    "total": 150,
    "new": 25,
    "read": 45,
    "replied": 60,
    "closed": 15,
    "spam": 5
  }
}
```

## 🎯 Testing Checklist

- [ ] Valid submission with all fields
- [ ] Valid submission with required fields only
- [ ] Domain restriction enforcement
- [ ] Email validation
- [ ] Required field validation
- [ ] Message length validation
- [ ] Spam detection functionality
- [ ] Rate limiting (multiple rapid submissions)
- [ ] CORS headers validation
- [ ] Admin authentication
- [ ] Status updates
- [ ] Spam marking
- [ ] Submission deletion

## 💡 Tips for Testing

1. **Use different Origins** to test domain restrictions
2. **Include realistic User-Agent** headers
3. **Test with various email formats** for validation
4. **Try rapid submissions** to test rate limiting
5. **Use spam keywords** to test detection
6. **Test with missing fields** for validation
7. **Verify CORS headers** in browser dev tools
8. **Check database** for proper data storage
