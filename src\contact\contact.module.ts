import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { ContactController } from './contact.controller';
import { ContactService } from './contact.service';
import { ContactSubmission } from '../database/models/contact-submission.model';
import { DomainRestrictionGuard } from '../common/guards/domain-restriction.guard';

@Module({
  imports: [
    SequelizeModule.forFeature([ContactSubmission])
  ],
  controllers: [ContactController],
  providers: [
    ContactService,
    DomainRestrictionGuard
  ],
  exports: [ContactService]
})
export class ContactModule {}
