#!/usr/bin/env node

/**
 * Model Analysis Script
 * Analyzes Sequelize models and extracts schema information
 */

const fs = require('fs');
const path = require('path');

// Configuration
const MODELS_PATH = path.join(__dirname, '../src/database/models');
const OUTPUT_PATH = path.join(__dirname, '../temp/model-analysis.json');

console.log('🔍 Analyzing Sequelize Models...');
console.log('================================');

// Model dependency mapping based on your codebase
const MODEL_DEPENDENCIES = {
  'role.model.ts': [],
  'language.model.ts': [],
  'answer-category.model.ts': [],
  'product-owner-model.ts': ['role.model.ts'],
  'company.model.ts': ['product-owner-model.ts'],
  'department.model.ts': ['company.model.ts'],
  'user.model.ts': ['role.model.ts', 'company.model.ts', 'department.model.ts'],
  'question.model.ts': [],
  'question-option.model.ts': ['question.model.ts', 'answer-category.model.ts'],
  'question-translation.model.ts': ['question.model.ts', 'language.model.ts'],
  'question-option-translation.model.ts': ['question-option.model.ts', 'language.model.ts'],
  'company-question-map.model.ts': ['company.model.ts', 'question.model.ts'],
  'survey-response.model.ts': ['user.model.ts', 'company.model.ts', 'question.model.ts', 'question-option.model.ts'],
  'report.model.ts': ['company.model.ts', 'user.model.ts'],
  'contact-submission.model.ts': []
};

// Table name mapping
const TABLE_NAMES = {
  'role.model.ts': 'Roles',
  'language.model.ts': 'Languages',
  'answer-category.model.ts': 'AnswerCategories',
  'product-owner-model.ts': 'ProductOwners',
  'company.model.ts': 'Companies',
  'department.model.ts': 'Departments',
  'user.model.ts': 'Users',
  'question.model.ts': 'Questions',
  'question-option.model.ts': 'QuestionOptions',
  'question-translation.model.ts': 'QuestionTranslations',
  'question-option-translation.model.ts': 'QuestionOptionTranslations',
  'company-question-map.model.ts': 'CompanyQuestionMaps',
  'survey-response.model.ts': 'SurveyResponses',
  'report.model.ts': 'Reports',
  'contact-submission.model.ts': 'ContactSubmissions'
};

// Basic column type mapping from your models
const MODEL_SCHEMAS = {
  'Roles': {
    id: { type: 'UUID', primaryKey: true, defaultValue: 'UUIDV4' },
    name: { type: 'STRING', allowNull: false },
    description: { type: 'STRING', allowNull: true },
    isActive: { type: 'BOOLEAN', defaultValue: true },
    isDeleted: { type: 'BOOLEAN', defaultValue: false },
    createdAt: { type: 'DATE', allowNull: false },
    updatedAt: { type: 'DATE', allowNull: false }
  },
  'Languages': {
    id: { type: 'UUID', primaryKey: true, defaultValue: 'UUIDV4' },
    name: { type: 'STRING(100)', allowNull: false },
    code: { type: 'STRING(10)', allowNull: false, unique: true },
    isActive: { type: 'BOOLEAN', defaultValue: true },
    isDefault: { type: 'BOOLEAN', defaultValue: false },
    createdAt: { type: 'DATE', allowNull: false },
    updatedAt: { type: 'DATE', allowNull: false }
  },
  'AnswerCategories': {
    id: { type: 'UUID', primaryKey: true, defaultValue: 'UUIDV4' },
    name: { type: 'STRING(100)', allowNull: false, unique: true },
    description: { type: 'TEXT', allowNull: true },
    color: { type: 'STRING(7)', allowNull: true },
    isActive: { type: 'BOOLEAN', defaultValue: true },
    createdAt: { type: 'DATE', allowNull: false },
    updatedAt: { type: 'DATE', allowNull: false }
  },
  'ProductOwners': {
    id: { type: 'UUID', primaryKey: true, defaultValue: 'UUIDV4' },
    firstName: { type: 'STRING(150)', allowNull: false },
    lastName: { type: 'STRING(150)', allowNull: false },
    email: { type: 'STRING(150)', allowNull: false, unique: true },
    password: { type: 'STRING(255)', allowNull: false },
    roleId: { type: 'UUID', allowNull: false, foreignKey: { table: 'Roles', key: 'id' } },
    refreshToken: { type: 'STRING', allowNull: true },
    isActive: { type: 'BOOLEAN', defaultValue: true },
    isDeleted: { type: 'BOOLEAN', defaultValue: false },
    createdAt: { type: 'DATE', allowNull: false },
    updatedAt: { type: 'DATE', allowNull: false },
    createdBy: { type: 'UUID', allowNull: true },
    updatedBy: { type: 'UUID', allowNull: true },
    deletedBy: { type: 'UUID', allowNull: true }
  },
  'Companies': {
    id: { type: 'UUID', primaryKey: true, defaultValue: 'UUIDV4' },
    name: { type: 'STRING(150)', allowNull: false },
    product_owner_id: { type: 'UUID', allowNull: false, foreignKey: { table: 'ProductOwners', key: 'id' } },
    contact_person_firstName: { type: 'STRING(150)', allowNull: false },
    contact_person_lastName: { type: 'STRING(150)', allowNull: false },
    contact_person_email: { type: 'STRING(150)', allowNull: false },
    contact_person_phone: { type: 'STRING(20)', allowNull: true },
    address_line: { type: 'STRING(255)', allowNull: false },
    zipcode: { type: 'STRING(20)', allowNull: false },
    country: { type: 'STRING(100)', allowNull: false },
    state: { type: 'STRING(100)', allowNull: false },
    city: { type: 'STRING(100)', allowNull: false },
    isActive: { type: 'BOOLEAN', defaultValue: true },
    isDeleted: { type: 'BOOLEAN', defaultValue: false },
    createdAt: { type: 'DATE', allowNull: false },
    updatedAt: { type: 'DATE', allowNull: false },
    createdBy: { type: 'UUID', allowNull: true },
    updatedBy: { type: 'UUID', allowNull: true },
    deletedBy: { type: 'UUID', allowNull: true }
  }
};

function analyzeModels() {
  try {
    console.log(`📁 Scanning models directory: ${MODELS_PATH}`);
    
    if (!fs.existsSync(MODELS_PATH)) {
      throw new Error(`Models directory not found: ${MODELS_PATH}`);
    }

    const modelFiles = fs.readdirSync(MODELS_PATH)
      .filter(file => file.endsWith('.model.ts'))
      .sort();

    console.log(`📋 Found ${modelFiles.length} model files:`);
    modelFiles.forEach(file => console.log(`  - ${file}`));

    // Create dependency-ordered list
    const orderedModels = resolveDependencyOrder(modelFiles);
    
    console.log('\n🔗 Dependency-resolved order:');
    orderedModels.forEach((file, index) => {
      const tableName = TABLE_NAMES[file] || file.replace('.model.ts', '');
      console.log(`  ${index + 1}. ${file} -> ${tableName}`);
    });

    // Create analysis result
    const analysis = {
      timestamp: new Date().toISOString(),
      totalModels: modelFiles.length,
      orderedModels: orderedModels.map((file, index) => ({
        order: index + 1,
        fileName: file,
        tableName: TABLE_NAMES[file] || file.replace('.model.ts', ''),
        dependencies: MODEL_DEPENDENCIES[file] || [],
        schema: MODEL_SCHEMAS[TABLE_NAMES[file]] || {}
      })),
      dependencies: MODEL_DEPENDENCIES
    };

    // Ensure temp directory exists
    const tempDir = path.dirname(OUTPUT_PATH);
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    // Save analysis
    fs.writeFileSync(OUTPUT_PATH, JSON.stringify(analysis, null, 2));
    
    console.log(`\n✅ Analysis complete! Results saved to: ${OUTPUT_PATH}`);
    console.log(`📊 Total models analyzed: ${analysis.totalModels}`);
    
    return analysis;

  } catch (error) {
    console.error('❌ Analysis failed:', error.message);
    throw error;
  }
}

function resolveDependencyOrder(modelFiles) {
  const resolved = [];
  const resolving = new Set();
  
  function resolve(file) {
    if (resolved.includes(file)) return;
    if (resolving.has(file)) {
      throw new Error(`Circular dependency detected involving ${file}`);
    }
    
    resolving.add(file);
    const dependencies = MODEL_DEPENDENCIES[file] || [];
    
    for (const dep of dependencies) {
      if (modelFiles.includes(dep)) {
        resolve(dep);
      }
    }
    
    resolving.delete(file);
    resolved.push(file);
  }
  
  for (const file of modelFiles) {
    resolve(file);
  }
  
  return resolved;
}

// Run analysis
if (require.main === module) {
  analyzeModels();
}

module.exports = { analyzeModels };
