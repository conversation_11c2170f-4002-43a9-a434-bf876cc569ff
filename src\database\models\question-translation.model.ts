import { Table, Column, Model, DataType, Foreign<PERSON>ey, BelongsTo, <PERSON><PERSON><PERSON>, PrimaryKey, AllowNull, CreatedAt, UpdatedAt } from 'sequelize-typescript';
import { Question } from './question.model';
import { Language } from './language.model';

@Table({
  tableName: 'QuestionTranslations',
  timestamps: true
})
export class QuestionTranslation extends Model<QuestionTranslation> {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column({ type: DataType.UUID })
  declare id: string;

  @ForeignKey(() => Question)
  @AllowNull(false)
  @Column(DataType.UUID)
  declare question_id: string;

  @ForeignKey(() => Language)
  @AllowNull(false)
  @Column(DataType.UUID)
  declare language_id: string;

  @AllowNull(false)
  @Column({ type: DataType.STRING(500) })
  declare question_text: string;

  @Default(true)
  @Column(DataType.BOOLEAN)
  declare isActive: boolean;

  @CreatedAt
  @Column(DataType.DATE)
  declare createdAt: Date;

  @Column(DataType.UUID)
  declare createdBy: string;

  @UpdatedAt
  @Column(DataType.DATE)
  declare updatedAt: Date;

  @Column(DataType.UUID)
  declare updatedBy: string;

  @Default(false)
  @Column(DataType.BOOLEAN)
  declare isDeleted: boolean;

  @Column(DataType.UUID)
  declare deletedBy: string;

  // Associations
  @BelongsTo(() => Question)
  question: Question;

  @BelongsTo(() => Language)
  language: Language;
}
