import { CorsOptions } from '@nestjs/common/interfaces/external/cors-options.interface';
import { getDomainConfig } from '../common/guards/domain-restriction.guard';

export const getCorsConfig = (): CorsOptions => {
  const { allowedDomains } = getDomainConfig();
  
  // Convert domain patterns to full URLs for CORS
  const allowedOrigins: string[] = allowedDomains.flatMap(domain => {
    if (domain.startsWith('*.')) {
      // For wildcard domains, we'll handle them in the origin function
      return [];
    }
    
    // Add both http and https for each domain
    const origins: string[] = [];
    
    if (domain.includes('localhost') || domain.includes('127.0.0.1')) {
      // For localhost, include common ports
      origins.push(`http://${domain}`);
      origins.push(`https://${domain}`);
      
      // Add common development ports
      const ports = ['3000', '3001', '4200', '8080'];
      ports.forEach(port => {
        if (!domain.includes(':')) {
          origins.push(`http://${domain}:${port}`);
          origins.push(`https://${domain}:${port}`);
        }
      });
    } else {
      origins.push(`https://${domain}`);
      origins.push(`http://${domain}`);
    }
    
    return origins;
  });

  return {
    origin: (origin, callback) => {
      // Allow requests with no origin (mobile apps, curl, etc.)
      if (!origin) {
        return callback(null, true);
      }

      // Check against allowed origins
      if (allowedOrigins.includes(origin)) {
        return callback(null, true);
      }

      // Check wildcard domains
      const wildcardDomains = allowedDomains.filter(domain => domain.startsWith('*.'));
      for (const wildcardDomain of wildcardDomains) {
        const baseDomain = wildcardDomain.substring(2);
        try {
          const originUrl = new URL(origin);
          if (originUrl.hostname.endsWith(baseDomain)) {
            return callback(null, true);
          }
        } catch (error) {
          // Invalid origin URL, continue checking
        }
      }

      // Log rejected origins for debugging
      console.warn(`CORS: Rejected origin: ${origin}`);
      console.warn(`CORS: Allowed origins:`, allowedOrigins);
      
      callback(new Error(`Origin ${origin} not allowed by CORS policy`));
    },
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: [
      'Origin',
      'X-Requested-With',
      'Content-Type',
      'Accept',
      'Authorization',
      'X-Forwarded-For',
      'X-Real-IP'
    ],
    credentials: false, // Set to true if you need to send cookies
    optionsSuccessStatus: 200,
    preflightContinue: false
  };
};

// Environment-specific CORS settings
export const getEnvironmentCorsConfig = () => {
  const environment = process.env.NODE_ENV || 'development';
  
  switch (environment) {
    case 'production':
      return {
        ...getCorsConfig(),
        credentials: false, // Be strict in production
        origin: getCorsConfig().origin // Use the domain restriction
      };
      
    case 'staging':
      return {
        ...getCorsConfig(),
        credentials: false,
        origin: getCorsConfig().origin
      };
      
    default: // development
      return {
        origin: true, // Allow all origins in development
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
        allowedHeaders: '*',
        credentials: true,
        optionsSuccessStatus: 200
      };
  }
};
