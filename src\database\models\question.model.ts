import { Table, Column, Model, DataType, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, AllowNull, CreatedAt, UpdatedAt } from 'sequelize-typescript';
import { QuestionOption } from './question-option.model';
import { CompanyQuestionMap } from './company-question-map.model';
import { QuestionTranslation } from './question-translation.model';

@Table({
  tableName: 'Questions',
  timestamps: true
})
export class Question extends Model<Question> {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column({ type: DataType.UUID })
  declare id: string;

  @AllowNull(false)
  @Column({ type: DataType.STRING(500) })
  declare question_text: string;

  @Default(true)
  @Column(DataType.BOOLEAN)
  declare isActive: boolean;

  @CreatedAt
  @Column(DataType.DATE)
  declare createdAt: Date;

  @Column(DataType.UUID)
  declare createdBy: string;

  @UpdatedAt
  @Column(DataType.DATE)
  declare updatedAt: Date;

  @Column(DataType.UUID)
  declare updatedBy: string;

  @De<PERSON><PERSON>(false)
  @Column(DataType.BOOLEAN)
  declare isDeleted: boolean;

  @Column(DataType.UUID)
  declare deletedBy: string;

  // Associations
  @HasMany(() => QuestionOption)
  options: QuestionOption[];

  @HasMany(() => CompanyQuestionMap)
  companyMaps: CompanyQuestionMap[];

  @HasMany(() => QuestionTranslation)
  translations: QuestionTranslation[];
}