'use strict';
const { v4: uuidv4 } = require('uuid');

// Generate consistent UUIDs for categories
const CATEGORY_IDS = {
  VERY_POSITIVE: uuidv4(),
  POSITIVE: uuidv4(),
  NEUTRAL: uuidv4(),
  NEGATIVE: uuidv4(),
  VERY_NEGATIVE: uuidv4()
};

module.exports = {
  up: async (queryInterface, Sequelize) => {
    console.log('🌱 Starting seeder: Populating AnswerCategories...');

    try {
      // Check if categories already exist
      const existingCategories = await queryInterface.sequelize.query(
        'SELECT COUNT(*) as count FROM AnswerCategories WHERE isDeleted = 0',
        { type: Sequelize.QueryTypes.SELECT }
      );

      if (existingCategories[0].count > 0) {
        console.log('✅ Answer categories already exist, skipping seeder');
        return;
      }

      // Insert predefined answer categories with real UUIDs
      await queryInterface.bulkInsert('AnswerCategories', [
        {
          id: CATEGORY_IDS.VERY_POSITIVE,
          name: 'VERY_POSITIVE',
          display_name: 'Very Positive',
          color_code: '#28a745',
          sort_order: 1,
          description: 'Responses indicating highest satisfaction, agreement, or positive sentiment. Examples: Excellent, Outstanding, Strongly Agree, Love it, Amazing',
          isActive: true,
          isDeleted: false,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: CATEGORY_IDS.POSITIVE,
          name: 'POSITIVE',
          display_name: 'Positive',
          color_code: '#17a2b8',
          sort_order: 2,
          description: 'Responses indicating good satisfaction, agreement, or positive sentiment. Examples: Good, Agree, Satisfied, Like it, Yes',
          isActive: true,
          isDeleted: false,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: CATEGORY_IDS.NEUTRAL,
          name: 'NEUTRAL',
          display_name: 'Neutral',
          color_code: '#ffc107',
          sort_order: 3,
          description: 'Responses indicating neutral sentiment or no strong opinion. Examples: Okay, Neither Agree nor Disagree, Average, Maybe, Unsure',
          isActive: true,
          isDeleted: false,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: CATEGORY_IDS.NEGATIVE,
          name: 'NEGATIVE',
          display_name: 'Negative',
          color_code: '#fd7e14',
          sort_order: 4,
          description: 'Responses indicating dissatisfaction, disagreement, or negative sentiment. Examples: Bad, Disagree, Unsatisfied, Dislike, No',
          isActive: true,
          isDeleted: false,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: CATEGORY_IDS.VERY_NEGATIVE,
          name: 'VERY_NEGATIVE',
          display_name: 'Very Negative',
          color_code: '#dc3545',
          sort_order: 5,
          description: 'Responses indicating highest dissatisfaction, strong disagreement, or very negative sentiment. Examples: Terrible, Awful, Strongly Disagree, Hate it, Horrible',
          isActive: true,
          isDeleted: false,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ]);

      console.log('✅ Answer categories seeded successfully');

      // Update existing question options with default categories if they exist
      console.log('🔧 Updating existing question options with default categories...');
      
      // Check if QuestionOptions table exists and has data
      const questionOptionsExist = await queryInterface.sequelize.query(
        `SELECT COUNT(*) as count FROM INFORMATION_SCHEMA.TABLES 
         WHERE TABLE_NAME = 'QuestionOptions'`,
        { type: Sequelize.QueryTypes.SELECT }
      );

      if (questionOptionsExist[0].count > 0) {
        const existingOptions = await queryInterface.sequelize.query(
          'SELECT COUNT(*) as count FROM QuestionOptions WHERE isDeleted = 0',
          { type: Sequelize.QueryTypes.SELECT }
        );

        if (existingOptions[0].count > 0) {
          // Map common option texts to categories (case-insensitive)
          const categoryMappings = [
            // Very Positive
            { patterns: ['excellent', 'outstanding', 'strongly agree', 'very satisfied', 'love it', 'amazing', 'perfect'], categoryId: CATEGORY_IDS.VERY_POSITIVE },

            // Positive
            { patterns: ['good', 'agree', 'satisfied', 'like it', 'yes', 'positive', 'happy', 'great'], categoryId: CATEGORY_IDS.POSITIVE },

            // Neutral
            { patterns: ['okay', 'ok', 'neutral', 'neither', 'average', 'maybe', 'unsure', 'fair'], categoryId: CATEGORY_IDS.NEUTRAL },

            // Negative
            { patterns: ['bad', 'disagree', 'unsatisfied', 'dislike', 'no', 'negative', 'unhappy', 'poor'], categoryId: CATEGORY_IDS.NEGATIVE },

            // Very Negative
            { patterns: ['terrible', 'awful', 'strongly disagree', 'very unsatisfied', 'hate it', 'horrible', 'worst'], categoryId: CATEGORY_IDS.VERY_NEGATIVE }
          ];

          // Apply mappings
          for (const mapping of categoryMappings) {
            for (const pattern of mapping.patterns) {
              await queryInterface.sequelize.query(`
                UPDATE QuestionOptions 
                SET category_id = '${mapping.categoryId}' 
                WHERE LOWER(option_text) LIKE '%${pattern}%' 
                AND (category_id IS NULL OR category_id = '')
                AND isDeleted = 0
              `);
            }
          }

          // Set remaining unmapped options to NEUTRAL as default
          await queryInterface.sequelize.query(`
            UPDATE QuestionOptions
            SET category_id = '${CATEGORY_IDS.NEUTRAL}'
            WHERE (category_id IS NULL OR category_id = '')
            AND isDeleted = 0
          `);

          console.log('✅ Existing question options updated with categories');
        }
      }

      console.log('🎉 Answer categories seeder completed successfully!');
    } catch (error) {
      console.error('❌ Error during answer categories seeding:', error);
      throw error;
    }
  },

  down: async (queryInterface) => {
    console.log('🔄 Rolling back seeder: Removing seeded answer categories...');

    // Remove only the seeded categories (by their specific IDs)
    await queryInterface.bulkDelete('AnswerCategories', {
      id: [
        CATEGORY_IDS.VERY_POSITIVE,
        CATEGORY_IDS.POSITIVE,
        CATEGORY_IDS.NEUTRAL,
        CATEGORY_IDS.NEGATIVE,
        CATEGORY_IDS.VERY_NEGATIVE
      ]
    });

    console.log('✅ Seeded answer categories removed successfully');
    console.log('🔙 Seeder rollback completed');
  }
};
