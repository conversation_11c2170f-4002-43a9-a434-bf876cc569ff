import { 
  Table, 
  Column, 
  Model, 
  DataType, 
  PrimaryKey, 
  Default, 
  AllowNull, 
  ForeignKey,
  BelongsTo,
  CreatedAt, 
  UpdatedAt,
  Index
} from 'sequelize-typescript';
import { User } from './user.model';
import { Company } from './company.model';
import { Question } from './question.model';
import { QuestionOption } from './question-option.model';

@Table({
  tableName: 'SurveyResponses',
  timestamps: true,
  indexes: [
    {
      name: 'idx_survey_responses_company_date',
      fields: ['company_id', 'response_date']
    },
    {
      name: 'idx_survey_responses_supervisor_date', 
      fields: ['supervisor_id', 'response_date']
    },
    {
      name: 'idx_survey_responses_user_date',
      fields: ['user_id', 'response_date']
    },
    {
      name: 'idx_survey_responses_session',
      fields: ['survey_session_id']
    },
    {
      name: 'idx_survey_responses_analytics',
      fields: ['company_id', 'question_id', 'response_date']
    }
  ]
})
export class SurveyResponse extends Model<SurveyResponse> {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column({ type: DataType.UUID })
  declare id: string;

  @ForeignKey(() => User)
  @AllowNull(false)
  @Column({ 
    type: DataType.UUID,
    comment: 'Employee who submitted the response'
  })
  declare user_id: string;

  @ForeignKey(() => Company)
  @AllowNull(false)
  @Column({ 
    type: DataType.UUID,
    comment: 'Company the response belongs to'
  })
  declare company_id: string;

  @ForeignKey(() => Question)
  @AllowNull(false)
  @Column({ 
    type: DataType.UUID,
    comment: 'Question that was answered'
  })
  declare question_id: string;

  @ForeignKey(() => QuestionOption)
  @AllowNull(false)
  @Column({ 
    type: DataType.UUID,
    comment: 'Selected answer option'
  })
  declare selected_option_id: string;

  @AllowNull(false)
  @Column({ 
    type: DataType.UUID,
    comment: 'Groups responses from the same survey session'
  })
  declare survey_session_id: string;

  @AllowNull(false)
  @Default(DataType.NOW)
  @Column({ 
    type: DataType.DATE,
    comment: 'When the response was submitted'
  })
  declare response_date: Date;

  @ForeignKey(() => User)
  @Column({ 
    type: DataType.UUID,
    comment: 'Supervisor of the responding employee at time of response'
  })
  declare supervisor_id: string;

  @Column({ 
    type: DataType.STRING(100),
    comment: 'Department of employee at time of response'
  })
  declare department: string;

  @Default(true)
  @Column(DataType.BOOLEAN)
  declare isActive: boolean;

  @CreatedAt
  @Column(DataType.DATE)
  declare createdAt: Date;

  @Column(DataType.UUID)
  declare createdBy: string;

  @UpdatedAt
  @Column(DataType.DATE)
  declare updatedAt: Date;

  @Column(DataType.UUID)
  declare updatedBy: string;

  @Default(false)
  @Column(DataType.BOOLEAN)
  declare isDeleted: boolean;

  @Column(DataType.UUID)
  declare deletedBy: string;

  // Associations
  @BelongsTo(() => User, 'user_id')
  user: User;

  @BelongsTo(() => Company)
  company: Company;

  @BelongsTo(() => Question)
  question: Question;

  @BelongsTo(() => QuestionOption, 'selected_option_id')
  selectedOption: QuestionOption;

  @BelongsTo(() => User, 'supervisor_id')
  supervisor: User;

  // Helper methods for analytics
  static async getResponsesByCompany(
    companyId: string, 
    startDate?: Date, 
    endDate?: Date
  ): Promise<SurveyResponse[]> {
    const whereClause: any = {
      company_id: companyId,
      isActive: true,
      isDeleted: false
    };

    if (startDate && endDate) {
      whereClause.response_date = {
        [require('sequelize').Op.between]: [startDate, endDate]
      };
    }

    return this.findAll({
      where: whereClause,
      include: [
        { model: User, as: 'user' },
        { model: Question },
        { model: QuestionOption, as: 'selectedOption' }
      ],
      order: [['response_date', 'DESC']]
    });
  }

  static async getResponsesBySupervisor(
    supervisorId: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<SurveyResponse[]> {
    const whereClause: any = {
      supervisor_id: supervisorId,
      isActive: true,
      isDeleted: false
    };

    if (startDate && endDate) {
      whereClause.response_date = {
        [require('sequelize').Op.between]: [startDate, endDate]
      };
    }

    return this.findAll({
      where: whereClause,
      include: [
        { model: User, as: 'user' },
        { model: Question },
        { model: QuestionOption, as: 'selectedOption' }
      ],
      order: [['response_date', 'DESC']]
    });
  }
}
