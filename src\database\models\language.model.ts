import { Table, Column, Model, DataType, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, AllowNull, CreatedAt, UpdatedAt } from 'sequelize-typescript';
import { QuestionTranslation } from './question-translation.model';
import { QuestionOptionTranslation } from './question-option-translation.model';

@Table({
  tableName: 'Languages',
  timestamps: true
})
export class Language extends Model<Language> {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column({ type: DataType.UUID })
  declare id: string;

  @AllowNull(false)
  @Column({ type: DataType.STRING(10), unique: true })
  declare code: string;

  @AllowNull(false)
  @Column({ type: DataType.STRING(100) })
  declare name: string;

  @Default(false)
  @Column(DataType.BOOLEAN)
  declare isDefault: boolean;

  @Default(true)
  @Column(DataType.BOOLEAN)
  declare isActive: boolean;

  @CreatedAt
  @Column(DataType.DATE)
  declare createdAt: Date;

  @Column(DataType.UUID)
  declare createdBy: string;

  @UpdatedAt
  @Column(DataType.DATE)
  declare updatedAt: Date;

  @Column(DataType.UUID)
  declare updatedBy: string;

  @Default(false)
  @Column(DataType.BOOLEAN)
  declare isDeleted: boolean;

  @Column(DataType.UUID)
  declare deletedBy: string;

  // Associations
  @HasMany(() => QuestionTranslation)
  questionTranslations: QuestionTranslation[];

  @HasMany(() => QuestionOptionTranslation)
  questionOptionTranslations: QuestionOptionTranslation[];
}
