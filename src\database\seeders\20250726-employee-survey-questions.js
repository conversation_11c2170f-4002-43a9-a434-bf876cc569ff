'use strict';

const { v4: uuidv4 } = require('uuid');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    console.log('🌱 Starting Employee Survey Questions seeder...');

    // Get existing categories
    const categories = await queryInterface.sequelize.query(
      `SELECT id, name FROM AnswerCategories WHERE isDeleted = 0`,
      { type: Sequelize.QueryTypes.SELECT }
    );

    const categoryMap = {};
    categories.forEach(cat => {
      categoryMap[cat.name] = cat.id;
    });

    console.log('📋 Found categories:', Object.keys(categoryMap));

    // Get languages
    const languages = await queryInterface.sequelize.query(
      `SELECT id, code FROM Languages WHERE isDeleted = 0`,
      { type: Sequelize.QueryTypes.SELECT }
    );

    const languageMap = {};
    languages.forEach(lang => {
      languageMap[lang.code] = lang.id;
    });

    console.log('🌐 Found languages:', Object.keys(languageMap));

    // Survey questions data
    const surveyQuestions = [
      {
        question_text: {
          en: "How satisfied are you with your current work environment?",
          es: "¿Qué tan satisfecho estás con tu ambiente de trabajo actual?"
        },
        options: [
          { text: { en: "Very Satisfied", es: "Muy Satisfecho" }, value: 5, category: "VERY_POSITIVE" },
          { text: { en: "Satisfied", es: "Satisfecho" }, value: 4, category: "POSITIVE" },
          { text: { en: "Neutral", es: "Neutral" }, value: 3, category: "NEUTRAL" },
          { text: { en: "Dissatisfied", es: "Insatisfecho" }, value: 2, category: "NEGATIVE" },
          { text: { en: "Very Dissatisfied", es: "Muy Insatisfecho" }, value: 1, category: "VERY_NEGATIVE" }
        ]
      },
      {
        question_text: {
          en: "How would you rate your work-life balance?",
          es: "¿Cómo calificarías tu equilibrio entre trabajo y vida personal?"
        },
        options: [
          { text: { en: "Excellent Balance", es: "Equilibrio Excelente" }, value: 5, category: "VERY_POSITIVE" },
          { text: { en: "Good Balance", es: "Buen Equilibrio" }, value: 4, category: "POSITIVE" },
          { text: { en: "Fair Balance", es: "Equilibrio Regular" }, value: 3, category: "NEUTRAL" },
          { text: { en: "Poor Balance", es: "Equilibrio Pobre" }, value: 2, category: "NEGATIVE" },
          { text: { en: "No Balance", es: "Sin Equilibrio" }, value: 1, category: "VERY_NEGATIVE" }
        ]
      },
      {
        question_text: {
          en: "How supportive is your direct manager?",
          es: "¿Qué tan comprensivo es tu supervisor directo?"
        },
        options: [
          { text: { en: "Extremely Supportive", es: "Extremadamente Comprensivo" }, value: 5, category: "VERY_POSITIVE" },
          { text: { en: "Very Supportive", es: "Muy Comprensivo" }, value: 4, category: "POSITIVE" },
          { text: { en: "Somewhat Supportive", es: "Algo Comprensivo" }, value: 3, category: "NEUTRAL" },
          { text: { en: "Not Very Supportive", es: "Poco Comprensivo" }, value: 2, category: "NEGATIVE" },
          { text: { en: "Not Supportive at All", es: "Nada Comprensivo" }, value: 1, category: "VERY_NEGATIVE" }
        ]
      },
      {
        question_text: {
          en: "How satisfied are you with career development opportunities?",
          es: "¿Qué tan satisfecho estás con las oportunidades de desarrollo profesional?"
        },
        options: [
          { text: { en: "Highly Satisfied", es: "Muy Satisfecho" }, value: 5, category: "VERY_POSITIVE" },
          { text: { en: "Satisfied", es: "Satisfecho" }, value: 4, category: "POSITIVE" },
          { text: { en: "Neither Satisfied nor Dissatisfied", es: "Ni Satisfecho ni Insatisfecho" }, value: 3, category: "NEUTRAL" },
          { text: { en: "Dissatisfied", es: "Insatisfecho" }, value: 2, category: "NEGATIVE" },
          { text: { en: "Highly Dissatisfied", es: "Muy Insatisfecho" }, value: 1, category: "VERY_NEGATIVE" }
        ]
      },
      {
        question_text: {
          en: "How effectively does your team collaborate?",
          es: "¿Qué tan efectivamente colabora tu equipo?"
        },
        options: [
          { text: { en: "Extremely Well", es: "Extremadamente Bien" }, value: 5, category: "VERY_POSITIVE" },
          { text: { en: "Very Well", es: "Muy Bien" }, value: 4, category: "POSITIVE" },
          { text: { en: "Adequately", es: "Adecuadamente" }, value: 3, category: "NEUTRAL" },
          { text: { en: "Poorly", es: "Mal" }, value: 2, category: "NEGATIVE" },
          { text: { en: "Very Poorly", es: "Muy Mal" }, value: 1, category: "VERY_NEGATIVE" }
        ]
      },
      {
        question_text: {
          en: "How effective is communication within your department?",
          es: "¿Qué tan efectiva es la comunicación dentro de tu departamento?"
        },
        options: [
          { text: { en: "Highly Effective", es: "Muy Efectiva" }, value: 5, category: "VERY_POSITIVE" },
          { text: { en: "Effective", es: "Efectiva" }, value: 4, category: "POSITIVE" },
          { text: { en: "Moderately Effective", es: "Moderadamente Efectiva" }, value: 3, category: "NEUTRAL" },
          { text: { en: "Ineffective", es: "Inefectiva" }, value: 2, category: "NEGATIVE" },
          { text: { en: "Highly Ineffective", es: "Muy Inefectiva" }, value: 1, category: "VERY_NEGATIVE" }
        ]
      },
      {
        question_text: {
          en: "Overall, how satisfied are you with your job?",
          es: "En general, ¿qué tan satisfecho estás con tu trabajo?"
        },
        options: [
          { text: { en: "Extremely Satisfied", es: "Extremadamente Satisfecho" }, value: 5, category: "VERY_POSITIVE" },
          { text: { en: "Very Satisfied", es: "Muy Satisfecho" }, value: 4, category: "POSITIVE" },
          { text: { en: "Moderately Satisfied", es: "Moderadamente Satisfecho" }, value: 3, category: "NEUTRAL" },
          { text: { en: "Slightly Dissatisfied", es: "Ligeramente Insatisfecho" }, value: 2, category: "NEGATIVE" },
          { text: { en: "Very Dissatisfied", es: "Muy Insatisfecho" }, value: 1, category: "VERY_NEGATIVE" }
        ]
      },
      {
        question_text: {
          en: "How would you rate the training and development programs?",
          es: "¿Cómo calificarías los programas de capacitación y desarrollo?"
        },
        options: [
          { text: { en: "Excellent", es: "Excelente" }, value: 5, category: "VERY_POSITIVE" },
          { text: { en: "Good", es: "Bueno" }, value: 4, category: "POSITIVE" },
          { text: { en: "Fair", es: "Regular" }, value: 3, category: "NEUTRAL" },
          { text: { en: "Poor", es: "Malo" }, value: 2, category: "NEGATIVE" },
          { text: { en: "Very Poor", es: "Muy Malo" }, value: 1, category: "VERY_NEGATIVE" }
        ]
      },
      {
        question_text: {
          en: "How satisfied are you with recognition for your work?",
          es: "¿Qué tan satisfecho estás con el reconocimiento por tu trabajo?"
        },
        options: [
          { text: { en: "Very Satisfied", es: "Muy Satisfecho" }, value: 5, category: "VERY_POSITIVE" },
          { text: { en: "Satisfied", es: "Satisfecho" }, value: 4, category: "POSITIVE" },
          { text: { en: "Neutral", es: "Neutral" }, value: 3, category: "NEUTRAL" },
          { text: { en: "Dissatisfied", es: "Insatisfecho" }, value: 2, category: "NEGATIVE" },
          { text: { en: "Very Dissatisfied", es: "Muy Insatisfecho" }, value: 1, category: "VERY_NEGATIVE" }
        ]
      }
    ];

    const createdBy = 'system-seeder';
    const now = new Date();

    // Process each question
    for (let i = 0; i < surveyQuestions.length; i++) {
      const questionData = surveyQuestions[i];
      const questionId = uuidv4();

      console.log(`📝 Creating question ${i + 1}: ${questionData.question_text.en.substring(0, 50)}...`);

      // Create question
      await queryInterface.bulkInsert('Questions', [{
        id: questionId,
        question_text: questionData.question_text.en,
        isActive: true,
        createdAt: now,
        createdBy: createdBy,
        updatedAt: now,
        isDeleted: false
      }]);

      // Create question translations
      const questionTranslations = [];
      for (const [langCode, text] of Object.entries(questionData.question_text)) {
        if (languageMap[langCode]) {
          questionTranslations.push({
            id: uuidv4(),
            question_id: questionId,
            language_id: languageMap[langCode],
            question_text: text,
            isActive: true,
            createdAt: now,
            createdBy: createdBy,
            updatedAt: now,
            isDeleted: false
          });
        }
      }

      if (questionTranslations.length > 0) {
        await queryInterface.bulkInsert('QuestionTranslations', questionTranslations);
      }

      // Create options
      for (const optionData of questionData.options) {
        const optionId = uuidv4();
        const categoryId = categoryMap[optionData.category];

        if (!categoryId) {
          console.warn(`⚠️  Category ${optionData.category} not found, skipping option`);
          continue;
        }

        // Create option
        await queryInterface.bulkInsert('QuestionOptions', [{
          id: optionId,
          question_id: questionId,
          option_text: optionData.text.en,
          option_value: optionData.value,
          category_id: categoryId,
          isActive: true,
          createdAt: now,
          createdBy: createdBy,
          updatedAt: now,
          isDeleted: false
        }]);

        // Create option translations
        const optionTranslations = [];
        for (const [langCode, text] of Object.entries(optionData.text)) {
          if (languageMap[langCode]) {
            optionTranslations.push({
              id: uuidv4(),
              question_option_id: optionId,
              language_id: languageMap[langCode],
              option_text: text,
              isActive: true,
              createdAt: now,
              createdBy: createdBy,
              updatedAt: now,
              isDeleted: false
            });
          }
        }

        if (optionTranslations.length > 0) {
          await queryInterface.bulkInsert('QuestionOptionTranslations', optionTranslations);
        }
      }
    }

    console.log('✅ Employee Survey Questions seeder completed successfully!');
  },

  down: async (queryInterface, Sequelize) => {
    console.log('🗑️  Rolling back Employee Survey Questions seeder...');
    
    // Delete in reverse order to maintain referential integrity
    await queryInterface.bulkDelete('QuestionOptionTranslations', {
      createdBy: 'system-seeder'
    });
    
    await queryInterface.bulkDelete('QuestionTranslations', {
      createdBy: 'system-seeder'
    });
    
    await queryInterface.bulkDelete('QuestionOptions', {
      createdBy: 'system-seeder'
    });
    
    await queryInterface.bulkDelete('Questions', {
      createdBy: 'system-seeder'
    });

    console.log('✅ Employee Survey Questions rollback completed!');
  }
};
