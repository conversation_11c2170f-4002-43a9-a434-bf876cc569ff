import { Table, Column, Model, DataType, Foreign<PERSON>ey, BelongsTo, <PERSON><PERSON>ult, PrimaryKey, AllowNull, CreatedAt, UpdatedAt, HasMany } from 'sequelize-typescript';
import { Question } from './question.model';
import { AnswerCategory } from './answer-category.model';
import { QuestionOptionTranslation } from './question-option-translation.model';

@Table({
  tableName: 'QuestionOptions',
  timestamps: true
})
export class QuestionOption extends Model<QuestionOption> {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column({ type: DataType.UUID })
  declare id: string;

  @ForeignKey(() => Question)
  @AllowNull(false)
  @Column(DataType.UUID)
  declare question_id: string;

  @AllowNull(false)
  @Column({ type: DataType.STRING(255) })
  declare option_text: string;

  @AllowNull(false)
  @Column(DataType.INTEGER)
  declare option_value: number;

  @ForeignKey(() => AnswerCategory)
  @AllowNull(false)
  @Column({
    type: DataType.UUID,
    comment: 'Foreign key to AnswerCategories table'
  })
  declare category_id: string;

  @Default(true)
  @Column(DataType.BOOLEAN)
  declare isActive: boolean;

  @CreatedAt
  @Column(DataType.DATE)
  declare createdAt: Date;

  @Column(DataType.UUID)
  declare createdBy: string;

  @UpdatedAt
  @Column(DataType.DATE)
  declare updatedAt: Date;

  @Column(DataType.UUID)
  declare updatedBy: string;

  @Default(false)
  @Column(DataType.BOOLEAN)
  declare isDeleted: boolean;

  @Column(DataType.UUID)
  declare deletedBy: string;

  // Associations
  @BelongsTo(() => Question)
  question: Question;

  @BelongsTo(() => AnswerCategory)
  category: AnswerCategory;

  @HasMany(() => QuestionOptionTranslation)
  translations: QuestionOptionTranslation[];

  // Helper method to get options by category
  static async getOptionsByCategory(categoryName: string): Promise<QuestionOption[]> {
    return this.findAll({
      where: {
        isActive: true,
        isDeleted: false
      },
      include: [{
        model: AnswerCategory,
        where: {
          name: categoryName,
          isActive: true,
          isDeleted: false
        }
      }]
    });
  }
}