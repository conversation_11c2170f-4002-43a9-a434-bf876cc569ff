import { IsString, IsNotEmpty, IsOptional, IsUUID, IsBoolean, MaxLength, IsEmail, IsIn, IsArray, ArrayMinSize } from 'class-validator';

export class CreateUserDto {
  @IsString()
  @IsNotEmpty()
  @MaxLength(150)
  firstName: string;

  @IsString()
  @IsNotEmpty()
  @MaxLength(150)
  lastName: string;

  @IsEmail()
  @IsNotEmpty()
  @MaxLength(150)
  email: string;

  @IsString()
  @IsNotEmpty()
  @IsIn(['Employee', 'CompanyAdministrator', 'CompanyManager', 'Supervisor'])
  role: string;

  @IsOptional()
  @IsUUID('4')
  departmentId?: string; // For single department roles (Employee, Supervisor, Manager)

  @IsOptional()
  @IsArray()
  @IsUUID('4', { each: true })
  @ArrayMinSize(1)
  departmentIds?: string[]; // For multi-department roles (CompanyAdministrator)

  @IsOptional()
  @IsUUID('4')
  supervisorId?: string;

  @IsOptional()
  @IsUUID('4')
  createdBy?: string;
}

export class UpdateUserDto {
  @IsString()
  @IsOptional()
  @MaxLength(150)
  firstName?: string;

  @IsString()
  @IsOptional()
  @MaxLength(150)
  lastName?: string;

  @IsEmail()
  @IsOptional()
  @MaxLength(150)
  email?: string;

  @IsString()
  @IsOptional()
  @IsIn(['Employee', 'CompanyAdministrator', 'CompanyManager', 'Supervisor'])
  role?: string;

  @IsOptional()
  @IsUUID('4')
  departmentId?: string; // For single department roles

  @IsOptional()
  @IsArray()
  @IsUUID('4', { each: true })
  departmentIds?: string[]; // For multi-department roles (CompanyAdministrator)

  @IsOptional()
  @IsUUID('4')
  supervisorId?: string;

  @IsBoolean()
  @IsOptional()
  isActive?: boolean;

  @IsOptional()
  @IsUUID('4')
  updatedBy?: string;
}

export class UserResponseDto {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  companyId?: string;
  role: {
    id: string;
    name: string;
  };
  department?: {
    id: string;
    name: string;
  };
  departments?: {
    id: string;
    name: string;
  }[]; // For multi-department access (CompanyAdministrator)
  supervisor?: {
    id: string;
    firstName: string;
    lastName: string;
    role: string;
  };
  isActive: boolean;
  isTemporaryPassword: boolean;
  hasSubmittedSurveyToday?: boolean; // Flag to indicate if user has submitted survey today
  createdAt: Date;
  updatedAt?: Date;
  createdBy?: string;
  updatedBy?: string;
}

export class ChangePasswordDto {
  @IsString()
  @IsNotEmpty()
  currentPassword: string;

  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  newPassword: string;

  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  confirmPassword: string;
}

export class UserListResponseDto {
  users: UserResponseDto[];
  pagination?: {
    total?: number;
    page?: number;
    limit?: number;
    totalPages?: number;
  };
}
