import { 
  Table, 
  Column, 
  Model, 
  <PERSON>Type, 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  AllowNull, 
  CreatedAt, 
  UpdatedAt,
  HasMany,
  Unique
} from 'sequelize-typescript';
import { QuestionOption } from './question-option.model';

@Table({
  tableName: 'AnswerCategories',
  timestamps: true
})
export class AnswerCategory extends Model<AnswerCategory> {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column({ type: DataType.UUID })
  declare id: string;

  @AllowNull(false)
  @Unique
  @Column({ 
    type: DataType.STRING(50),
    comment: 'Category name (VERY_POSITIVE, POSITIVE, etc.)'
  })
  declare name: string;

  @AllowNull(false)
  @Column({ 
    type: DataType.STRING(100),
    comment: 'Human readable name for display'
  })
  declare display_name: string;

  @AllowNull(false)
  @Column({ 
    type: DataType.STRING(7),
    comment: 'Hex color code for graph visualization'
  })
  declare color_code: string;

  @AllowNull(false)
  @Default(0)
  @Column({ 
    type: DataType.INTEGER,
    comment: 'Order for consistent sorting'
  })
  declare sort_order: number;

  @Column({ 
    type: DataType.TEXT,
    comment: 'Detailed description of the category'
  })
  declare description: string;

  @Default(true)
  @Column(DataType.BOOLEAN)
  declare isActive: boolean;

  @CreatedAt
  @Column(DataType.DATE)
  declare createdAt: Date;

  @Column(DataType.UUID)
  declare createdBy: string;

  @UpdatedAt
  @Column(DataType.DATE)
  declare updatedAt: Date;

  @Column(DataType.UUID)
  declare updatedBy: string;

  @Default(false)
  @Column(DataType.BOOLEAN)
  declare isDeleted: boolean;

  @Column(DataType.UUID)
  declare deletedBy: string;

  // Associations
  @HasMany(() => QuestionOption)
  questionOptions: QuestionOption[];

  // Static methods for common categories
  static readonly CATEGORIES = {
    VERY_POSITIVE: 'VERY_POSITIVE',
    POSITIVE: 'POSITIVE', 
    NEUTRAL: 'NEUTRAL',
    NEGATIVE: 'NEGATIVE',
    VERY_NEGATIVE: 'VERY_NEGATIVE'
  } as const;

  // Helper method to get category by name
  static async getCategoryByName(name: string): Promise<AnswerCategory | null> {
    return this.findOne({
      where: { 
        name, 
        isActive: true, 
        isDeleted: false 
      }
    });
  }

  // Helper method to get all active categories ordered by sort_order
  static async getAllActiveCategories(): Promise<AnswerCategory[]> {
    return this.findAll({
      where: { 
        isActive: true, 
        isDeleted: false 
      },
      order: [['sort_order', 'ASC']]
    });
  }
}
