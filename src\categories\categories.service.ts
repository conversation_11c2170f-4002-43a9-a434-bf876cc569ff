import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Sequelize } from 'sequelize-typescript';
import { CreationAttributes, QueryTypes } from 'sequelize';
import { AnswerCategory } from '../database/models/answer-category.model';
import { QuestionOption } from '../database/models/question-option.model';
import { SurveyResponse } from '../database/models/survey-response.model';
import { CreateCategoryDto, UpdateCategoryDto, CategoryUsageStatsDto } from './dto/category.dto';

@Injectable()
export class CategoriesService {
  constructor(
    @InjectModel(AnswerCategory)
    private readonly answerCategoryModel: typeof AnswerCategory,
    @InjectModel(QuestionOption)
    private readonly questionOptionModel: typeof QuestionOption,
    @InjectModel(SurveyResponse)
    private readonly surveyResponseModel: typeof SurveyResponse,
    private readonly sequelize: Sequelize,
  ) {}

  /**
   * Get all active categories ordered by sort_order
   */
  async getAllActiveCategories(): Promise<AnswerCategory[]> {
    return this.answerCategoryModel.findAll({
      where: {
        isActive: true,
        isDeleted: false
      },
      order: [['sort_order', 'ASC']]
    });
  }

  /**
   * Get category by ID
   */
  async getCategoryById(id: string): Promise<AnswerCategory | null> {
    return this.answerCategoryModel.findOne({
      where: {
        id,
        isActive: true,
        isDeleted: false
      }
    });
  }

  /**
   * Get category by name
   */
  async getCategoryByName(name: string): Promise<AnswerCategory | null> {
    return this.answerCategoryModel.findOne({
      where: {
        name,
        isActive: true,
        isDeleted: false
      }
    });
  }

  /**
   * Create new category
   */
  async createCategory(createCategoryDto: CreateCategoryDto): Promise<AnswerCategory> {
    // Check if category with same name already exists
    const existingCategory = await this.answerCategoryModel.findOne({
      where: { name: createCategoryDto.name }
    });

    if (existingCategory) {
      throw new Error(`Category with name '${createCategoryDto.name}' already exists`);
    }

    return this.answerCategoryModel.create(createCategoryDto as CreationAttributes<AnswerCategory>);
  }

  /**
   * Update category
   */
  async updateCategory(id: string, updateCategoryDto: UpdateCategoryDto): Promise<AnswerCategory | null> {
    const category = await this.getCategoryById(id);
    if (!category) {
      return null;
    }

    // If updating name, check for duplicates
    if (updateCategoryDto.name && updateCategoryDto.name !== category.name) {
      const existingCategory = await this.answerCategoryModel.findOne({
        where: { 
          name: updateCategoryDto.name,
          id: { [require('sequelize').Op.ne]: id }
        }
      });

      if (existingCategory) {
        throw new Error(`Category with name '${updateCategoryDto.name}' already exists`);
      }
    }

    await category.update(updateCategoryDto);
    return category.reload();
  }

  /**
   * Soft delete category
   */
  async deleteCategory(id: string): Promise<boolean> {
    const category = await this.getCategoryById(id);
    if (!category) {
      return false;
    }

    // Check if category is being used by question options
    const usageCount = await this.questionOptionModel.count({
      where: {
        category_id: id,
        isActive: true,
        isDeleted: false
      }
    });

    if (usageCount > 0) {
      throw new Error(`Cannot delete category. It is being used by ${usageCount} question options.`);
    }

    await category.update({
      isDeleted: true,
      deletedBy: undefined // TODO: Get from current user context
    });

    return true;
  }

  /**
   * Get category usage statistics
   */
  async getCategoryUsageStats(): Promise<CategoryUsageStatsDto[]> {
    const query = `
      SELECT 
        ac.id,
        ac.name,
        ac.display_name,
        ac.color_code,
        ac.sort_order,
        COUNT(DISTINCT qo.id) as questionOptionsCount,
        COUNT(DISTINCT sr.id) as responseCount,
        MAX(sr.response_date) as lastUsed
      FROM AnswerCategories ac
      LEFT JOIN QuestionOptions qo ON ac.id = qo.category_id AND qo.isActive = 1 AND qo.isDeleted = 0
      LEFT JOIN SurveyResponses sr ON qo.id = sr.selected_option_id AND sr.isActive = 1 AND sr.isDeleted = 0
      WHERE ac.isActive = 1 AND ac.isDeleted = 0
      GROUP BY ac.id, ac.name, ac.display_name, ac.color_code, ac.sort_order
      ORDER BY ac.sort_order
    `;

    const results = await this.sequelize.query(query, {
      type: QueryTypes.SELECT
    });

    return results.map((row: any) => ({
      id: row.id,
      name: row.name,
      display_name: row.display_name,
      color_code: row.color_code,
      sort_order: row.sort_order,
      questionOptionsCount: parseInt(row.questionOptionsCount) || 0,
      responseCount: parseInt(row.responseCount) || 0,
      lastUsed: row.lastUsed ? new Date(row.lastUsed) : undefined
    }));
  }

  /**
   * Reorder categories
   */
  async reorderCategories(reorderData: { categoryId: string; newOrder: number }[]): Promise<void> {
    const transaction = await this.sequelize.transaction();

    try {
      for (const item of reorderData) {
        await this.answerCategoryModel.update(
          { sort_order: item.newOrder },
          { 
            where: { id: item.categoryId },
            transaction
          }
        );
      }

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * Get categories with their question options
   */
  async getCategoriesWithOptions(): Promise<AnswerCategory[]> {
    return this.answerCategoryModel.findAll({
      where: {
        isActive: true,
        isDeleted: false
      },
      include: [
        {
          model: QuestionOption,
          where: {
            isActive: true,
            isDeleted: false
          },
          required: false
        }
      ],
      order: [['sort_order', 'ASC']]
    });
  }

  /**
   * Bulk update question options with categories
   */
  async bulkUpdateQuestionOptionsCategories(
    mappings: { optionId: string; categoryId: string }[]
  ): Promise<void> {
    const transaction = await this.sequelize.transaction();

    try {
      for (const mapping of mappings) {
        await this.questionOptionModel.update(
          { category_id: mapping.categoryId },
          { 
            where: { id: mapping.optionId },
            transaction
          }
        );
      }

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }
}
