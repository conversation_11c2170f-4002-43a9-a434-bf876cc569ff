import { Injectable, UnauthorizedException, BadRequestException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcrypt';
import { User } from 'src/database/models/user.model';
import { Role } from 'src/database/models/role.model';
import { Department } from 'src/database/models/department.model';
import { UserDepartment } from 'src/database/models/user-department.model';
import { ChangePasswordDto } from 'src/users/dto/user.dto';

@Injectable()
export class UserAuthService {
  constructor(private jwtService: JwtService) { }

  async validateUser(email: string, pass: string): Promise<any> {
    const user = await User.findOne({ 
      where: { 
        email,
        isActive: true,
        isDeleted: false
      } 
    });

    console.log("user", user);
    console.log("pass", pass)

    if (!user?.dataValues) {
      throw new UnauthorizedException('Invalid credentials');
    }
    console.log("user11111====>", user.dataValues);


    // Check if password exists before comparing
    if (!user?.dataValues?.password || !pass) {
      throw new UnauthorizedException('Invalid credentials');
    }
    console.log("user2222====>", user.dataValues.password );


    const isPasswordValid = await bcrypt.compare(pass, user?.dataValues.password);

    console.log("isPasswordValid ===>", isPasswordValid);
    if (!isPasswordValid) {
      throw new UnauthorizedException('Invalid credentials');
    }
    
    // Check if user is CompanyAdministrator or Supervisor
    const role = await Role.findByPk(user?.dataValues?.roleId);
    console.log("role===>", role)
    if (!role) {
      throw new UnauthorizedException('Unauthorized role');
    }
    
    return user;
  }
  
  async login(user: User, infiniteToken: boolean = false) {
    const role: any = await Role.findByPk(user?.dataValues?.roleId);

    console.log("user=====================>", user)
    console.log("rolllllll===============>", role)

    if (!role.dataValues) {
      throw new UnauthorizedException('Role not found');
    }

    // Get user with department and supervisor info for complete response
    let fullUser: any = null;
    let supervisor: any = null;
    let userDepartments: any[] = [];
    let primaryDepartment: any = null;

    try {
      fullUser = await User.findByPk(user.id, {
        include: [{ model: Role }]
      });

      // Get user departments through junction table
      userDepartments = await UserDepartment.findAll({
        where: {
          user_id: user.id,
          isDeleted: false,
          isActive: true
        },
        include: [{ model: Department }]
      });

      // Set primary department (first active department)
      if (userDepartments.length > 0) {
        primaryDepartment = userDepartments[0].department;
      }

      if (fullUser?.supervisor_id) {
        supervisor = await User.findByPk(fullUser.supervisor_id, {
          include: [{ model: Role }]
        });
      }
    } catch (error) {
      console.log('Error fetching user details:', error);
      // Fallback: use basic user info if associations fail
      fullUser = user;
    }

    const payload = {
      email: user.email,
      sub: user.id,
      role: role.dataValues.name,
      companyId: user?.dataValues?.company_id,
      department_id: primaryDepartment?.id || null
    };

    // Check if user is CompanyAdmin and infiniteToken is requested
    const isCompanyAdmin = role.dataValues.name === 'CompanyAdmin';
    const shouldGenerateInfiniteToken = infiniteToken && isCompanyAdmin;

    // Security check: Only CompanyAdmin can request infinite tokens
    if (infiniteToken && !isCompanyAdmin) {
      console.warn(`⚠️ Non-CompanyAdmin user ${user.email} attempted to request infinite token`);
      throw new UnauthorizedException('Infinite tokens are only available for Company Administrators');
    }

    const accessToken = this.jwtService.sign(payload, shouldGenerateInfiniteToken ? {} : {
      expiresIn: '30m',
    });

    const refreshToken = this.jwtService.sign(payload, shouldGenerateInfiniteToken ? {} : {
      expiresIn: '7d',
    });

    const hashedRefreshToken = await bcrypt.hash(refreshToken, 10);
    await user.update({ refreshToken: hashedRefreshToken });

    // Log infinite token generation
    if (shouldGenerateInfiniteToken) {
      console.log(`🔓 Infinite token generated for CompanyAdmin: ${user.email}`);
    }

    const simplifiedUser = user.get({ plain: true })

    const response = {
      access_token: accessToken,
      refresh_token: refreshToken,
      user: {
        id: simplifiedUser.id,
        firstName: simplifiedUser.firstName,
        lastName: simplifiedUser.lastName,
        email: simplifiedUser.email,
        role: {
          id: role.id,
          name: role.dataValues.name,
        },
        department: primaryDepartment ? {
          id: primaryDepartment.id,
          name: primaryDepartment.name,
        } : null,
        supervisor: supervisor ? {
          id: supervisor.id,
          firstName: supervisor.firstName,
          lastName: supervisor.lastName,
          role: supervisor.role?.name || 'Unknown',
        } : undefined,
        companyId: simplifiedUser.company_id
      },
      requiresPasswordChange: user.isTemporaryPassword || false,
      infiniteToken: shouldGenerateInfiniteToken
    };

    // Add message if password change is required
    if (user.isTemporaryPassword) {
      response['message'] = 'You must change your password before proceeding';
    }

    return response;
  }

  async refreshTokens(userId: string, refreshToken: string) {
    const user = await User.findByPk(userId);

    if (!user || !user.refreshToken) {
      throw new UnauthorizedException('Access Denied');
    }

    const isMatch = await bcrypt.compare(refreshToken, user.refreshToken);
    if (!isMatch) {
      throw new UnauthorizedException('Invalid refresh token');
    }

    const role: any = await Role.findByPk(user.roleId);
    if (!role) {
      throw new UnauthorizedException('Role not found');
    }

    // Get user's primary department
    let primaryDepartmentId: string | null = null;
    try {
      const userDepartment = await UserDepartment.findOne({
        where: {
          user_id: userId,
          isDeleted: false,
          isActive: true
        }
      });
      primaryDepartmentId = userDepartment?.department_id || null;
    } catch (error) {
      console.log('Error fetching user department:', error);
    }

    const payload = {
      email: user.email,
      sub: user.id,
      role: role.dataValues.name,
      companyId: user.company_id,
      department_id: primaryDepartmentId
    };
    
    const newAccessToken = this.jwtService.sign(payload, { expiresIn: '30m' });
    const newRefreshToken = this.jwtService.sign(payload, { expiresIn: '7d' });

    const hashedNewRefreshToken = await bcrypt.hash(newRefreshToken, 10);
    await user.update({ refreshToken: hashedNewRefreshToken });

    return {
      access_token: newAccessToken,
      refresh_token: newRefreshToken,
    };
  }

  /**
   * Change password for users with temporary passwords
   */
  async forceChangePassword(
    userId: string,
    currentPassword: string,
    newPassword: string,
    confirmPassword: string
  ) {
    // Validate new password matches confirmation
    if (newPassword !== confirmPassword) {
      throw new BadRequestException('New password and confirmation do not match');
    }

    // Get user
    const user = await User.findByPk(userId);
    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    // Verify current password
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password);
    if (!isCurrentPasswordValid) {
      throw new UnauthorizedException('Current password is incorrect');
    }

    // Ensure user has temporary password
    if (!user.isTemporaryPassword) {
      throw new BadRequestException('This endpoint is only for users with temporary passwords');
    }

    // Hash new password
    const hashedNewPassword = await bcrypt.hash(newPassword, 10);

    // Update user password and remove temporary flag
    await user.update({
      password: hashedNewPassword,
      isTemporaryPassword: false,
    });

    // Generate new tokens
    const role: any = await Role.findByPk(user.roleId);
    if (!role) {
      throw new UnauthorizedException('Role not found');
    }

    // Get user's primary department
    let primaryDepartmentId: string | null = null;
    try {
      const userDepartment = await UserDepartment.findOne({
        where: {
          user_id: userId,
          isDeleted: false,
          isActive: true
        }
      });
      primaryDepartmentId = userDepartment?.department_id || null;
    } catch (error) {
      console.log('Error fetching user department:', error);
    }

    const payload = {
      email: user.email,
      sub: user.id,
      role: role.dataValues.name,
      companyId: user.company_id,
      department_id: primaryDepartmentId
    };

    const accessToken = this.jwtService.sign(payload, {
      expiresIn: '30m',
    });

    const refreshToken = this.jwtService.sign(payload, {
      expiresIn: '7d',
    });

    const hashedRefreshToken = await bcrypt.hash(refreshToken, 10);
    await user.update({ refreshToken: hashedRefreshToken });

    return {
      message: 'Password changed successfully',
      access_token: accessToken,
      refresh_token: refreshToken,
    };
  }

  /**
   * Regular password change for authenticated users
   */
  async changePassword(
    userId: string,
    currentPassword: string,
    newPassword: string,
    confirmPassword: string
  ) {
    // Validate new password matches confirmation
    if (newPassword !== confirmPassword) {
      throw new BadRequestException('New password and confirmation do not match');
    }

    // Get user
    const user = await User.findByPk(userId);
    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    // Verify current password
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password);
    if (!isCurrentPasswordValid) {
      throw new UnauthorizedException('Current password is incorrect');
    }

    // Hash new password
    const hashedNewPassword = await bcrypt.hash(newPassword, 10);

    // Update user password
    await user.update({
      password: hashedNewPassword,
      isTemporaryPassword: false, // Ensure temporary flag is removed
    });

    return {
      message: 'Password changed successfully',
    };
  }
}


