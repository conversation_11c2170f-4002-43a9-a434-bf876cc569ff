import { IsEmail, IsS<PERSON>, <PERSON><PERSON>ptional, IsBoolean } from 'class-validator';

export class LoginDto {
  @IsEmail()
  email: string;

  @IsString()
  password: string;

  @IsOptional()
  @IsBoolean()
  infiniteToken?: boolean;
}

export class LoginResponseDto {
  access_token: string;
  refresh_token: string;
  user: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    role: {
      id: string;
      name: string;
    };
    department?: {
      id: string;
      name: string;
    } | null;
    supervisor?: {
      id: string;
      firstName: string;
      lastName: string;
      role: string;
    };
    companyId: string;
  };
  requiresPasswordChange: boolean;
  infiniteToken?: boolean;
  message?: string;
}