import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Sequelize } from 'sequelize-typescript';
import { CreationAttributes } from 'sequelize';
import { v4 as uuidv4 } from 'uuid';
import { SurveyResponse } from '../database/models/survey-response.model';
import { QuestionOption } from '../database/models/question-option.model';
import { Question } from '../database/models/question.model';
import { User } from '../database/models/user.model';
import { Company } from '../database/models/company.model';

export interface SurveySubmissionDto {
  userId: string;
  companyId: string;
  responses: {
    questionId: string;
    selectedOptionId: string;
  }[];
  department?: string;
}

@Injectable()
export class SurveyService {
  constructor(
    @InjectModel(SurveyResponse)
    private readonly surveyResponseModel: typeof SurveyResponse,
    @InjectModel(QuestionOption)
    private readonly questionOptionModel: typeof QuestionOption,
    @InjectModel(Question)
    private readonly questionModel: typeof Question,
    @InjectModel(User)
    private readonly userModel: typeof User,
    @InjectModel(Company)
    private readonly companyModel: typeof Company,
    private readonly sequelize: Sequelize,
  ) {}

  /**
   * Submit a complete survey response
   */
  async submitSurvey(submissionData: SurveySubmissionDto): Promise<{ sessionId: string; responseCount: number }> {
    const transaction = await this.sequelize.transaction();

    try {
      // Validate user and company
      const user = await this.userModel.findByPk(submissionData.userId);
      if (!user) {
        throw new Error('User not found');
      }

      const company = await this.companyModel.findByPk(submissionData.companyId);
      if (!company) {
        throw new Error('Company not found');
      }

      // Check if user has already submitted a survey today
      const hasSubmittedToday = await this.hasUserSubmittedSurveyToday(
        submissionData.userId,
        submissionData.companyId
      );

      if (hasSubmittedToday) {
        throw new Error('You have already submitted a survey today. Only one survey submission per day is allowed.');
      }

      // Generate session ID for this survey submission
      const sessionId = uuidv4();
      const responseDate = new Date();

      // Get user's supervisor (if any)
      const supervisorId = user.supervisor_id;
      const department = submissionData.department || 'Unknown';

      // Validate all questions and options exist
      for (const response of submissionData.responses) {
        const question = await this.questionModel.findByPk(response.questionId);
        if (!question) {
          throw new Error(`Question ${response.questionId} not found`);
        }

        const option = await this.questionOptionModel.findByPk(response.selectedOptionId);
        if (!option) {
          throw new Error(`Question option ${response.selectedOptionId} not found`);
        }

        if (option.question_id !== response.questionId) {
          throw new Error(`Option ${response.selectedOptionId} does not belong to question ${response.questionId}`);
        }
      }

      // Create survey responses
      const surveyResponses: SurveyResponse[] = [];
      for (const response of submissionData.responses) {
        const surveyResponse = await this.surveyResponseModel.create({
          user_id: submissionData.userId,
          company_id: submissionData.companyId,
          question_id: response.questionId,
          selected_option_id: response.selectedOptionId,
          survey_session_id: sessionId,
          response_date: responseDate,
          supervisor_id: supervisorId,
          department: department,
          createdBy: submissionData.userId
        } as CreationAttributes<SurveyResponse>, { transaction });

        surveyResponses.push(surveyResponse);
      }

      await transaction.commit();

      return {
        sessionId,
        responseCount: surveyResponses.length
      };
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * Get user's survey history
   */
  async getUserSurveyHistory(userId: string, limit: number = 10): Promise<any[]> {
    const sessions = await this.surveyResponseModel.findAll({
      where: {
        user_id: userId,
        isActive: true,
        isDeleted: false
      },
      attributes: [
        'survey_session_id',
        'response_date',
        [this.sequelize.fn('COUNT', this.sequelize.col('id')), 'responseCount']
      ],
      group: ['survey_session_id', 'response_date'],
      order: [['response_date', 'DESC']],
      limit
    });

    return sessions;
  }

  /**
   * Get survey responses for a specific session
   */
  async getSurveySessionDetails(sessionId: string): Promise<SurveyResponse[]> {
    return this.surveyResponseModel.findAll({
      where: {
        survey_session_id: sessionId,
        isActive: true,
        isDeleted: false
      },
      include: [
        {
          model: Question,
          attributes: ['id', 'question_text']
        },
        {
          model: QuestionOption,
          as: 'selectedOption',
          attributes: ['id', 'option_text', 'option_value'],
          include: [
            {
              model: require('../database/models/answer-category.model').AnswerCategory,
              as: 'category',
              attributes: ['id', 'name', 'display_name', 'color_code']
            }
          ]
        }
      ],
      order: [['createdAt', 'ASC']]
    });
  }

  /**
   * Check if user has already submitted survey for a specific period
   */
  async hasUserSubmittedSurvey(
    userId: string,
    companyId: string,
    startDate: Date,
    endDate: Date
  ): Promise<boolean> {
    const existingResponse = await this.surveyResponseModel.findOne({
      where: {
        user_id: userId,
        company_id: companyId,
        response_date: {
          [require('sequelize').Op.between]: [startDate, endDate]
        },
        isActive: true,
        isDeleted: false
      }
    });

    return !!existingResponse;
  }

  /**
   * Check if user has already submitted survey today
   */
  async hasUserSubmittedSurveyToday(userId: string, companyId: string): Promise<boolean> {
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 0, 0, 0, 0);
    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59, 999);

    return this.hasUserSubmittedSurvey(userId, companyId, startOfDay, endOfDay);
  }

  /**
   * Get survey completion rate for a company
   */
  async getCompanySurveyCompletionRate(
    companyId: string, 
    startDate: Date, 
    endDate: Date
  ): Promise<{ totalEmployees: number; completedSurveys: number; completionRate: number }> {
    const totalEmployees = await this.userModel.count({
      where: {
        company_id: companyId,
        isActive: true,
        isDeleted: false
      }
    });

    const completedSurveys = await this.surveyResponseModel.count({
      where: {
        company_id: companyId,
        response_date: {
          [require('sequelize').Op.between]: [startDate, endDate]
        },
        isActive: true,
        isDeleted: false
      },
      distinct: true,
      col: 'user_id'
    });

    const completionRate = totalEmployees > 0 ? (completedSurveys / totalEmployees) * 100 : 0;

    return {
      totalEmployees,
      completedSurveys,
      completionRate
    };
  }

  /**
   * Delete survey responses (soft delete)
   */
  async deleteSurveySession(sessionId: string, deletedBy: string): Promise<boolean> {
    const transaction = await this.sequelize.transaction();

    try {
      const [affectedRows] = await this.surveyResponseModel.update(
        {
          isDeleted: true,
          deletedBy: deletedBy
        },
        {
          where: {
            survey_session_id: sessionId,
            isDeleted: false
          },
          transaction
        }
      );

      await transaction.commit();
      return affectedRows > 0;
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }
}
