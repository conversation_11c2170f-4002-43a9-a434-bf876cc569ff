'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Create enum type for status if using Postgres
    if (queryInterface.sequelize.getDialect() === 'postgres') {
      await queryInterface.sequelize.query(`
        CREATE TYPE enum_ContactSubmissions_status AS ENUM ('new', 'read', 'replied', 'closed');
      `);
    }

    await queryInterface.createTable('ContactSubmissions', {
      id: {
        type: Sequelize.UUID,
        primaryKey: true,
        allowNull: false,
        defaultValue: Sequelize.UUIDV4
      },
      firstName: {
        type: Sequelize.STRING(100),
        allowNull: false,
        comment: 'Contact person first name'
      },
      lastName: {
        type: Sequelize.STRING(100),
        allowNull: false,
        comment: 'Contact person last name'
      },
      email: {
        type: Sequelize.STRING(255),
        allowNull: false,
        validate: { isEmail: true },
        comment: 'Contact email address'
      },
      phone: {
        type: Sequelize.STRING(20),
        allowNull: true,
        comment: 'Contact phone number'
      },
      subject: {
        type: Sequelize.STRING(255),
        allowNull: false,
        comment: 'Contact subject/topic'
      },
      message: {
        type: Sequelize.TEXT,
        allowNull: false,
        comment: 'Contact message content'
      },
      ipAddress: {
        type: Sequelize.STRING(45),
        allowNull: true,
        comment: 'IP address of the submitter'
      },
      userAgent: {
        type: Sequelize.STRING(500),
        allowNull: true,
        comment: 'Browser user agent string'
      },
      refererDomain: {
        type: Sequelize.STRING(255),
        allowNull: true,
        comment: 'Domain that submitted the form'
      },
      status: {
        type: queryInterface.sequelize.getDialect() === 'postgres' 
          ? 'enum_ContactSubmissions_status'
          : Sequelize.ENUM('new', 'read', 'replied', 'closed'),
        allowNull: false,
        defaultValue: 'new',
        comment: 'Status of the contact submission'
      },
      isSpam: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        comment: 'Whether this submission is marked as spam'
      },
      isDeleted: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn('GETDATE')
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.fn('GETDATE')
      },
      deletedAt: {
        type: Sequelize.DATE,
        allowNull: true
      }
    });
  },

  down: async (queryInterface) => {
    await queryInterface.dropTable('ContactSubmissions');

    if (queryInterface.sequelize.getDialect() === 'postgres') {
      await queryInterface.sequelize.query(`
        DROP TYPE IF EXISTS enum_ContactSubmissions_status;
      `);
    }
  }
};
