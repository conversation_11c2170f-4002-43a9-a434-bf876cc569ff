import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { SurveyController } from './survey.controller';
import { SurveyService } from './survey.service';
import { SurveyResponse } from '../database/models/survey-response.model';
import { QuestionOption } from '../database/models/question-option.model';
import { Question } from '../database/models/question.model';
import { User } from '../database/models/user.model';
import { Company } from '../database/models/company.model';

@Module({
  imports: [
    SequelizeModule.forFeature([
      SurveyResponse,
      QuestionOption,
      Question,
      User,
      Company
    ])
  ],
  controllers: [SurveyController],
  providers: [SurveyService],
  exports: [SurveyService]
})
export class SurveyModule {}
