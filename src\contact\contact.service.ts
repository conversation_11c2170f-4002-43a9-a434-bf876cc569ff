import { Injectable, Logger, BadRequestException, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { ContactSubmission, ContactSubmissionStatus } from '../database/models/contact-submission.model';
import { CreateContactSubmissionDto, ContactSubmissionResponseDto, UpdateContactSubmissionStatusDto } from './dto/create-contact-submission.dto';
import { Op, CreationAttributes } from 'sequelize';

@Injectable()
export class ContactService {
  private readonly logger = new Logger(ContactService.name);

  constructor(
    @InjectModel(ContactSubmission)
    private contactSubmissionModel: typeof ContactSubmission,
  ) {}

  async createSubmission(
    createContactDto: CreateContactSubmissionDto,
    domainInfo?: any
  ): Promise<ContactSubmissionResponseDto> {
    try {
      this.logger.log(`Creating new contact submission from: ${createContactDto.email}`);

      // Basic spam detection
      const isSpam = await this.detectSpam(createContactDto, domainInfo);

      const phone = createContactDto.phone || null;

      // Create the submission
      const submission = await this.contactSubmissionModel.create({
        firstName: createContactDto.firstName,
        lastName: createContactDto.lastName,
        email: createContactDto.email,
        phone: phone,
        subject: createContactDto.subject,
        message: createContactDto.message,
        ipAddress: domainInfo?.ip,
        userAgent: domainInfo?.userAgent,
        refererDomain: domainInfo?.domain,
        status: ContactSubmissionStatus.NEW,
        isSpam: isSpam
      } as CreationAttributes<ContactSubmission>);

      this.logger.log(`Contact submission created successfully: ${submission.id}`);

      // Log for monitoring
      this.logger.debug('Contact submission details:', {
        id: submission.id,
        email: createContactDto.email,
        domain: domainInfo?.domain,
        ip: domainInfo?.ip,
        isSpam
      });

      return new ContactSubmissionResponseDto(submission.get({ plain: true }));
    } catch (error) {
      this.logger.error('Error creating contact submission:', error);
      throw new BadRequestException('Failed to submit contact form. Please try again.');
    }
  }

  async getAllSubmissions(
    page: number = 1,
    limit: number = 20,
    status?: ContactSubmissionStatus,
    includeSpam: boolean = false
  ): Promise<{
    submissions: ContactSubmissionResponseDto[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    try {
      const offset = (page - 1) * limit;
      const whereClause: any = {
        isDeleted: false
      };

      if (status) {
        whereClause.status = status;
      }

      if (!includeSpam) {
        whereClause.isSpam = false;
      }

      const { count, rows } = await this.contactSubmissionModel.findAndCountAll({
        where: whereClause,
        order: [['createdAt', 'DESC']],
        limit,
        offset
      });

      const submissions = rows.map(submission => new ContactSubmissionResponseDto(submission.get({ plain: true })));

      return {
        submissions,
        total: count,
        page,
        totalPages: Math.ceil(count / limit)
      };
    } catch (error) {
      this.logger.error('Error fetching contact submissions:', error);
      throw new BadRequestException('Failed to fetch contact submissions');
    }
  }

  async getSubmissionById(id: string): Promise<ContactSubmissionResponseDto> {
    try {
      const submission = await this.contactSubmissionModel.findOne({
        where: {
          id,
          isDeleted: false
        }
      });

      if (!submission) {
        throw new NotFoundException(`Contact submission with ID ${id} not found`);
      }

      // Mark as read if it's new
      if (submission.status === ContactSubmissionStatus.NEW) {
        await submission.update({ status: ContactSubmissionStatus.READ });
      }

      return new ContactSubmissionResponseDto(submission.get({ plain: true }));
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error('Error fetching contact submission:', error);
      throw new BadRequestException('Failed to fetch contact submission');
    }
  }

  async updateSubmissionStatus(
    id: string,
    updateStatusDto: UpdateContactSubmissionStatusDto
  ): Promise<ContactSubmissionResponseDto> {
    try {
      const submission = await this.contactSubmissionModel.findOne({
        where: {
          id,
          isDeleted: false
        }
      });

      if (!submission) {
        throw new NotFoundException(`Contact submission with ID ${id} not found`);
      }

      await submission.update({
        status: updateStatusDto.status as ContactSubmissionStatus
      });

      this.logger.log(`Contact submission ${id} status updated to: ${updateStatusDto.status}`);

      return new ContactSubmissionResponseDto(submission.get({ plain: true }));
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error('Error updating contact submission status:', error);
      throw new BadRequestException('Failed to update contact submission status');
    }
  }

  async deleteSubmission(id: string): Promise<{ message: string }> {
    try {
      const submission = await this.contactSubmissionModel.findOne({
        where: {
          id,
          isDeleted: false
        }
      });

      if (!submission) {
        throw new NotFoundException(`Contact submission with ID ${id} not found`);
      }

      await submission.update({
        isDeleted: true,
        deletedAt: new Date()
      });

      this.logger.log(`Contact submission ${id} deleted successfully`);

      return { message: 'Contact submission deleted successfully' };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error('Error deleting contact submission:', error);
      throw new BadRequestException('Failed to delete contact submission');
    }
  }

  async markAsSpam(id: string): Promise<ContactSubmissionResponseDto> {
    try {
      const submission = await this.contactSubmissionModel.findOne({
        where: {
          id,
          isDeleted: false
        }
      });

      if (!submission) {
        throw new NotFoundException(`Contact submission with ID ${id} not found`);
      }

      await submission.update({ isSpam: true });

      this.logger.log(`Contact submission ${id} marked as spam`);

      return new ContactSubmissionResponseDto(submission.get({ plain: true }));
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error('Error marking submission as spam:', error);
      throw new BadRequestException('Failed to mark submission as spam');
    }
  }

  private async detectSpam(
    createContactDto: CreateContactSubmissionDto,
    domainInfo?: any
  ): Promise<boolean> {
    // Simple spam detection rules
    const spamKeywords = ['viagra', 'casino', 'lottery', 'winner', 'congratulations', 'click here', 'free money'];
    const message = createContactDto.message.toLowerCase();
    const subject = createContactDto.subject.toLowerCase();

    // Check for spam keywords
    const hasSpamKeywords = spamKeywords.some(keyword => 
      message.includes(keyword) || subject.includes(keyword)
    );

    // Check for excessive links
    const linkCount = (createContactDto.message.match(/https?:\/\//g) || []).length;
    const hasExcessiveLinks = linkCount > 3;

    // Check for duplicate submissions from same IP in last hour
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
    const recentSubmissions = await this.contactSubmissionModel.count({
      where: {
        ipAddress: domainInfo?.ip,
        createdAt: {
          [Op.gte]: oneHourAgo
        }
      }
    });

    const hasDuplicateSubmissions = recentSubmissions >= 3;

    return hasSpamKeywords || hasExcessiveLinks || hasDuplicateSubmissions;
  }

  async getSubmissionStats(): Promise<{
    total: number;
    new: number;
    read: number;
    replied: number;
    closed: number;
    spam: number;
  }> {
    try {
      const [total, newCount, readCount, repliedCount, closedCount, spamCount] = await Promise.all([
        this.contactSubmissionModel.count({ where: { isDeleted: false } }),
        this.contactSubmissionModel.count({ where: { status: ContactSubmissionStatus.NEW, isDeleted: false } }),
        this.contactSubmissionModel.count({ where: { status: ContactSubmissionStatus.READ, isDeleted: false } }),
        this.contactSubmissionModel.count({ where: { status: ContactSubmissionStatus.REPLIED, isDeleted: false } }),
        this.contactSubmissionModel.count({ where: { status: ContactSubmissionStatus.CLOSED, isDeleted: false } }),
        this.contactSubmissionModel.count({ where: { isSpam: true, isDeleted: false } })
      ]);

      return {
        total,
        new: newCount,
        read: readCount,
        replied: repliedCount,
        closed: closedCount,
        spam: spamCount
      };
    } catch (error) {
      this.logger.error('Error fetching submission stats:', error);
      throw new BadRequestException('Failed to fetch submission statistics');
    }
  }
}
