import { Controller, Post, Body, UnauthorizedException } from '@nestjs/common';
import { AuthService } from './auth.service';

@Controller('product-owner-auth')
export class ProductOwnerAuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('login')
  async login(@Body() body: { email: string; password: string }) {
    const user = await this.authService.validateProductOwner(body.email, body.password);
    return this.authService.loginProductOwner(user);
  }

  @Post('refresh-token')
  async refreshToken(@Body() body: { userId: string; refreshToken: string }) {
    return this.authService.refreshProductOwnerTokens(body.userId, body.refreshToken);
  }
}
