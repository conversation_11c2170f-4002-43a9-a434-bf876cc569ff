import {
  Table,
  Column,
  Model,
  DataType,
  Primary<PERSON>ey,
  Default,
  ForeignKey,
  BelongsTo,
  AllowNull,
  CreatedAt,
  UpdatedAt,
} from 'sequelize-typescript';
import { User } from './user.model';
import { Department } from './department.model';

@Table({
  tableName: 'UserDepartments',
  timestamps: true,
})
export class UserDepartment extends Model<UserDepartment> {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column({ type: DataType.UUID })
  declare id: string;

  @ForeignKey(() => User)
  @AllowNull(false)
  @Column(DataType.UUID)
  user_id: string;

  @ForeignKey(() => Department)
  @AllowNull(false)
  @Column(DataType.UUID)
  department_id: string;

  @Default(true)
  @Column(DataType.BOOLEAN)
  isActive: boolean;

  @CreatedAt
  @Column(DataType.DATE)
  declare createdAt: Date;

  @Column(DataType.UUID)
  createdBy: string;

  @UpdatedAt
  @Column(DataType.DATE)
  declare updatedAt: Date;

  @Column(DataType.UUID)
  updatedBy: string;

  @Default(false)
  @Column(DataType.BOOLEAN)
  isDeleted: boolean;

  @Column(DataType.UUID)
  deletedBy: string;

  // Associations
  @BelongsTo(() => User)
  user: User;

  @BelongsTo(() => Department)
  department: Department;
}
