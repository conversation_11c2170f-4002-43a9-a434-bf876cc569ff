import { <PERSON>du<PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { QuestionsController } from './questions.controller';
import { QuestionsService } from './questions.service';
import { Question } from '../database/models/question.model';
import { QuestionOption } from '../database/models/question-option.model';
import { CommonModule } from '../common/common.module';

@Module({
  imports: [
    SequelizeModule.forFeature([
      Question,
      QuestionOption
    ]),
    CommonModule
  ],
  controllers: [QuestionsController],
  providers: [QuestionsService],
  exports: [QuestionsService]
})
export class QuestionsModule {}
