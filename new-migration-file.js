'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Remove the old column if it exists
    await queryInterface.removeColumn('Companies', 'contact_person_name');
    
    // Add the new columns
    await queryInterface.addColumn('Companies', 'contact_person_firstName', {
      type: Sequelize.STRING(150),
      allowNull: false,
      defaultValue: '' // Temporary default value
    });
    
    await queryInterface.addColumn('Companies', 'contact_person_lastName', {
      type: Sequelize.STRING(150),
      allowNull: false,
      defaultValue: '' // Temporary default value
    });
  },

  down: async (queryInterface, Sequelize) => {
    // Revert changes
    await queryInterface.removeColumn('Companies', 'contact_person_firstName');
    await queryInterface.removeColumn('Companies', 'contact_person_lastName');
    
    await queryInterface.addColumn('Companies', 'contact_person_name', {
      type: Sequelize.STRING(150),
      allowNull: false
    });
  }
};