#!/usr/bin/env node

/**
 * Script to seed employee survey questions
 * Run with: node scripts/seed-survey-questions.js
 */

const { execSync } = require('child_process');
const path = require('path');

console.log('🌱 Employee Survey Questions Seeder');
console.log('=====================================');

try {
  // Change to project root directory
  process.chdir(path.join(__dirname, '..'));
  
  console.log('📋 Prerequisites check...');
  
  // Check if languages seeder has been run
  console.log('1️⃣  Checking if languages are seeded...');
  
  // Check if categories seeder has been run
  console.log('2️⃣  Checking if answer categories are seeded...');
  
  console.log('🚀 Running employee survey questions seeder...');
  
  // Run the specific seeder
  execSync('npx sequelize-cli db:seed --seed 20250726-employee-survey-questions.js', {
    stdio: 'inherit',
    cwd: process.cwd()
  });
  
  console.log('✅ Employee survey questions seeded successfully!');
  console.log('');
  console.log('📊 Summary:');
  console.log('- 10 comprehensive survey questions created');
  console.log('- 50 answer options with proper categorization');
  console.log('- Full English and Spanish translations');
  console.log('- Ready for employee surveys!');
  console.log('');
  console.log('🔗 Next steps:');
  console.log('1. Assign questions to companies using the API');
  console.log('2. Test the survey endpoints');
  console.log('3. Start collecting employee feedback!');
  
} catch (error) {
  console.error('❌ Error running seeder:', error.message);
  console.log('');
  console.log('🔧 Troubleshooting:');
  console.log('1. Make sure you have run the languages seeder first:');
  console.log('   npx sequelize-cli db:seed --seed *languages*');
  console.log('');
  console.log('2. Make sure you have run the categories seeder first:');
  console.log('   npx sequelize-cli db:seed --seed *categories*');
  console.log('');
  console.log('3. Check your database connection in config/config.json');
  
  process.exit(1);
}
