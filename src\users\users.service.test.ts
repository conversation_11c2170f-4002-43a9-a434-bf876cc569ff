import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/sequelize';
import { UsersService } from './users.service';
import { User } from '../database/models/user.model';
import { Role } from '../database/models/role.model';
import { Company } from '../database/models/company.model';
import { Department } from '../database/models/department.model';
import { SurveyResponse } from '../database/models/survey-response.model';
import { EmailService } from '../common/services/email.service';
import { SurveyService } from '../survey/survey.service';
import { NotFoundException } from '@nestjs/common';

describe('UsersService - Delete User with Survey Responses', () => {
  let service: UsersService;
  let userModel: typeof User;
  let surveyResponseModel: typeof SurveyResponse;

  const mockUser = {
    id: 'user-123',
    company_id: 'company-123',
    isDeleted: false,
    update: jest.fn(),
  };

  const mockUserModel = {
    findOne: jest.fn(),
  };

  const mockSurveyResponseModel = {
    update: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UsersService,
        {
          provide: getModelToken(User),
          useValue: mockUserModel,
        },
        {
          provide: getModelToken(Role),
          useValue: {},
        },
        {
          provide: getModelToken(Company),
          useValue: {},
        },
        {
          provide: getModelToken(Department),
          useValue: {},
        },
        {
          provide: getModelToken(SurveyResponse),
          useValue: mockSurveyResponseModel,
        },
        {
          provide: EmailService,
          useValue: {},
        },
        {
          provide: SurveyService,
          useValue: {},
        },
      ],
    }).compile();

    service = module.get<UsersService>(UsersService);
    userModel = module.get<typeof User>(getModelToken(User));
    surveyResponseModel = module.get<typeof SurveyResponse>(getModelToken(SurveyResponse));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('deleteUser', () => {
    it('should soft delete user and their survey responses', async () => {
      // Arrange
      const userId = 'user-123';
      const companyId = 'company-123';
      const deletedByUserId = 'admin-123';

      mockUserModel.findOne.mockResolvedValue(mockUser);
      mockUser.update.mockResolvedValue(mockUser);
      mockSurveyResponseModel.update.mockResolvedValue([2]); // 2 affected rows

      // Act
      const result = await service.deleteUser(userId, companyId, deletedByUserId);

      // Assert
      expect(mockUserModel.findOne).toHaveBeenCalledWith({
        where: {
          id: userId,
          company_id: companyId,
          isDeleted: false,
        },
      });

      expect(mockUser.update).toHaveBeenCalledWith({
        isDeleted: true,
        deletedBy: deletedByUserId,
        isActive: false,
      });

      expect(mockSurveyResponseModel.update).toHaveBeenCalledWith(
        {
          isDeleted: true,
          deletedBy: deletedByUserId,
          isActive: false,
        },
        {
          where: {
            user_id: userId,
            isDeleted: false,
          },
        }
      );

      expect(result).toEqual({
        message: 'User and associated survey responses deleted successfully',
      });
    });

    it('should throw NotFoundException when user not found', async () => {
      // Arrange
      const userId = 'non-existent-user';
      const companyId = 'company-123';
      const deletedByUserId = 'admin-123';

      mockUserModel.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(
        service.deleteUser(userId, companyId, deletedByUserId)
      ).rejects.toThrow(NotFoundException);

      expect(mockUserModel.findOne).toHaveBeenCalledWith({
        where: {
          id: userId,
          company_id: companyId,
          isDeleted: false,
        },
      });

      expect(mockUser.update).not.toHaveBeenCalled();
      expect(mockSurveyResponseModel.update).not.toHaveBeenCalled();
    });

    it('should handle case where user has no survey responses', async () => {
      // Arrange
      const userId = 'user-without-surveys';
      const companyId = 'company-123';
      const deletedByUserId = 'admin-123';

      mockUserModel.findOne.mockResolvedValue(mockUser);
      mockUser.update.mockResolvedValue(mockUser);
      mockSurveyResponseModel.update.mockResolvedValue([0]); // 0 affected rows

      // Act
      const result = await service.deleteUser(userId, companyId, deletedByUserId);

      // Assert
      expect(mockUser.update).toHaveBeenCalled();
      expect(mockSurveyResponseModel.update).toHaveBeenCalled();
      expect(result).toEqual({
        message: 'User and associated survey responses deleted successfully',
      });
    });
  });
});
