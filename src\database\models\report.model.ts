import { Table, Column, Model, DataType, ForeignKey, BelongsTo, <PERSON><PERSON>ult, PrimaryKey, AllowNull, CreatedAt, UpdatedAt } from 'sequelize-typescript';
import { Company } from './company.model';
import { User } from './user.model';

export enum ReportType {
  COMPANY_SURVEY_ANALYTICS = 'company_survey_analytics',
  SUPERVISOR_TEAM_ANALYTICS = 'supervisor_team_analytics',
  QUESTION_CATEGORY_BREAKDOWN = 'question_category_breakdown',
  TREND_ANALYSIS = 'trend_analysis',
  DEPARTMENT_ANALYTICS = 'department_analytics',
  EXPORT_DATA = 'export_data'
}

export enum ReportStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed'
}

export enum ReportFormat {
  JSON = 'json',
  CSV = 'csv',
  PDF = 'pdf',
  EXCEL = 'excel'
}

@Table({
  tableName: 'Reports',
  timestamps: true
})
export class Report extends Model<Report> {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column({ type: DataType.UUID })
  declare id: string;

  @AllowNull(false)
  @Column({ type: DataType.STRING(100) })
  declare name: string;

  @AllowNull(false)
  @Column({ 
    type: DataType.ENUM(...Object.values(ReportType)),
    comment: 'Type of report generated'
  })
  declare type: ReportType;

  @AllowNull(false)
  @Column({ 
    type: DataType.ENUM(...Object.values(ReportStatus)),
    defaultValue: ReportStatus.PENDING,
    comment: 'Current status of the report'
  })
  declare status: ReportStatus;

  @AllowNull(false)
  @Column({ 
    type: DataType.ENUM(...Object.values(ReportFormat)),
    defaultValue: ReportFormat.JSON,
    comment: 'Format of the report output'
  })
  declare format: ReportFormat;

  @ForeignKey(() => Company)
  @AllowNull(false)
  @Column({
    type: DataType.UUID,
    comment: 'Company this report belongs to'
  })
  declare company_id: string;

  @ForeignKey(() => User)
  @AllowNull(false)
  @Column({
    type: DataType.UUID,
    comment: 'User who requested the report'
  })
  declare requested_by: string;

  @AllowNull(false)
  @Column({
    type: DataType.DATE,
    comment: 'Start date for report data range'
  })
  declare start_date: Date;

  @AllowNull(false)
  @Column({
    type: DataType.DATE,
    comment: 'End date for report data range'
  })
  declare end_date: Date;

  @Column({
    type: DataType.JSON,
    comment: 'Additional parameters for report generation'
  })
  declare parameters: Record<string, any>;

  @Column({
    type: DataType.JSON,
    comment: 'Generated report data'
  })
  declare data: Record<string, any>;

  @Column({
    type: DataType.TEXT,
    comment: 'File path if report is saved as file'
  })
  declare file_path: string;

  @Column({
    type: DataType.INTEGER,
    comment: 'File size in bytes'
  })
  declare file_size: number;

  @Column({
    type: DataType.DATE,
    comment: 'When the report generation started'
  })
  declare processing_started_at: Date;

  @Column({
    type: DataType.DATE,
    comment: 'When the report generation completed'
  })
  declare processing_completed_at: Date;

  @Column({
    type: DataType.TEXT,
    comment: 'Error message if report generation failed'
  })
  declare error_message: string;

  @Column({
    type: DataType.DATE,
    comment: 'When the report expires and can be deleted'
  })
  declare expires_at: Date;

  @Default(true)
  @Column({
    type: DataType.BOOLEAN,
    comment: 'Whether the report is active'
  })
  declare isActive: boolean;

  @CreatedAt
  @Column(DataType.DATE)
  declare createdAt: Date;

  @Column(DataType.UUID)
  declare createdBy: string;

  @UpdatedAt
  @Column(DataType.DATE)
  declare updatedAt: Date;

  @Column(DataType.UUID)
  declare updatedBy: string;

  @Default(false)
  @Column(DataType.BOOLEAN)
  declare isDeleted: boolean;

  @Column(DataType.UUID)
  declare deletedBy: string;

  // Associations
  @BelongsTo(() => Company)
  company: Company;

  @BelongsTo(() => User, 'requested_by')
  requestedBy: User;

  // Virtual fields for computed properties
  get duration(): number | null {
    if (this.processing_started_at && this.processing_completed_at) {
      return this.processing_completed_at.getTime() - this.processing_started_at.getTime();
    }
    return null;
  }

  get isExpired(): boolean {
    if (!this.expires_at) return false;
    return new Date() > this.expires_at;
  }

  get isProcessing(): boolean {
    return this.status === ReportStatus.PROCESSING;
  }

  get isCompleted(): boolean {
    return this.status === ReportStatus.COMPLETED;
  }

  get isFailed(): boolean {
    return this.status === ReportStatus.FAILED;
  }
}
