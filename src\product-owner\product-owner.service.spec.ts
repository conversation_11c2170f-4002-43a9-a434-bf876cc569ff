import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/sequelize';
import { ProductOwnerService } from './product-owner.service';
import { Company } from '../database/models/company.model';
import { User } from '../database/models/user.model';
import { Role } from '../database/models/role.model';

describe('ProductOwnerService - getCompanies with Pagination', () => {
  let service: ProductOwnerService;
  let companyModel: typeof Company;

  const mockCompanies = [
    {
      id: 'company-1',
      name: 'Tech Corp',
      product_owner_id: 'owner-1',
      contact_person_firstName: '<PERSON>',
      contact_person_lastName: 'Doe',
      contact_person_email: '<EMAIL>',
      contact_person_phone: '************',
      address_line: '123 Tech Street',
      zipcode: '12345',
      country: 'USA',
      state: 'California',
      city: 'San Francisco',
      isActive: true,
      isDeleted: false,
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: 'user-1',
      updatedBy: 'user-1',
      get: jest.fn().mockReturnThis(),
    },
    {
      id: 'company-2',
      name: 'Business Inc',
      product_owner_id: 'owner-1',
      contact_person_firstName: 'Jane',
      contact_person_lastName: 'Smith',
      contact_person_email: '<EMAIL>',
      contact_person_phone: '************',
      address_line: '456 Business Ave',
      zipcode: '54321',
      country: 'USA',
      state: 'New York',
      city: 'New York',
      isActive: true,
      isDeleted: false,
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: 'user-1',
      updatedBy: 'user-1',
      get: jest.fn().mockReturnThis(),
    }
  ];

  const mockCompanyModel = {
    findAndCountAll: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ProductOwnerService,
        {
          provide: getModelToken(Company),
          useValue: mockCompanyModel,
        },
        {
          provide: getModelToken(User),
          useValue: {},
        },
        {
          provide: getModelToken(Role),
          useValue: {},
        },
      ],
    }).compile();

    service = module.get<ProductOwnerService>(ProductOwnerService);
    companyModel = module.get<typeof Company>(getModelToken(Company));
  });

  it('should return paginated companies with default pagination', async () => {
    // Mock the database response
    mockCompanyModel.findAndCountAll.mockResolvedValue({
      rows: mockCompanies,
      count: 2,
    });

    const result = await service.getCompanies();

    expect(result).toEqual({
      companies: expect.arrayContaining([
        expect.objectContaining({
          id: 'company-1',
          name: 'Tech Corp',
          country: 'USA',
          state: 'California',
          city: 'San Francisco',
        }),
        expect.objectContaining({
          id: 'company-2',
          name: 'Business Inc',
          country: 'USA',
          state: 'New York',
          city: 'New York',
        }),
      ]),
      pagination: {
        total: 2,
        page: 1,
        limit: 10,
        totalPages: 1,
      },
    });

    expect(mockCompanyModel.findAndCountAll).toHaveBeenCalledWith({
      where: { isDeleted: false },
      limit: 10,
      offset: 0,
      order: [['createdAt', 'DESC']],
    });
  });

  it('should return paginated companies with search functionality', async () => {
    mockCompanyModel.findAndCountAll.mockResolvedValue({
      rows: [mockCompanies[0]],
      count: 1,
    });

    const result = await service.getCompanies({
      page: 1,
      limit: 5,
      search: 'Tech',
      sortBy: 'name',
      sortOrder: 'ASC',
    });

    expect(result.pagination).toEqual({
      total: 1,
      page: 1,
      limit: 5,
      totalPages: 1,
    });

    expect(mockCompanyModel.findAndCountAll).toHaveBeenCalledWith({
      where: expect.objectContaining({
        isDeleted: false,
      }),
      limit: 5,
      offset: 0,
      order: [['name', 'ASC']],
    });
  });

  it('should return paginated companies with location filters', async () => {
    mockCompanyModel.findAndCountAll.mockResolvedValue({
      rows: [mockCompanies[0]],
      count: 1,
    });

    const result = await service.getCompanies({
      country: 'USA',
      state: 'California',
      city: 'San Francisco',
      isActive: true,
    });

    expect(result.companies).toHaveLength(1);
    expect(mockCompanyModel.findAndCountAll).toHaveBeenCalledWith({
      where: expect.objectContaining({
        isDeleted: false,
        isActive: true,
      }),
      limit: 10,
      offset: 0,
      order: [['createdAt', 'DESC']],
    });
  });
});
