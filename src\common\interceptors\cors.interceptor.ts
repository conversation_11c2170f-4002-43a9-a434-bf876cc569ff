import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';

@Injectable()
export class CorsInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const response = context.switchToHttp().getResponse();
    const request = context.switchToHttp().getRequest();

    // Set CORS headers
    response.header('Access-Control-Allow-Origin', '*');
    response.header('Access-Control-Allow-Methods', 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS');
    response.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, Accept, Origin, X-Requested-With');
    response.header('Access-Control-Max-Age', '3600');

    // Handle preflight OPTIONS request
    if (request.method === 'OPTIONS') {
      response.status(200).send();
      return new Observable(subscriber => {
        subscriber.next(null);
        subscriber.complete();
      });
    }

    return next.handle().pipe(
      tap(() => {
        // Additional processing if needed
      }),
    );
  }
}
