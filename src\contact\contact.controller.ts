import {
  Controller,
  Post,
  Get,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Req,
  HttpCode,
  HttpStatus,
  Logger,
  ValidationPipe,
  UsePipes
} from '@nestjs/common';
import { Request } from 'express';
import { ContactService } from './contact.service';
import { CreateContactSubmissionDto, ContactSubmissionResponseDto, UpdateContactSubmissionStatusDto } from './dto/create-contact-submission.dto';
import { DomainRestrictionGuard, AllowedDomains, getDomainConfig } from '../common/guards/domain-restriction.guard';
import { JwtAuthGuard } from 'src/auth/guards/jwt-auth-guards';

@Controller('contact')
export class ContactController {
  private readonly logger = new Logger(ContactController.name);

  constructor(private readonly contactService: ContactService) {}

  // Public endpoint - Domain restricted, no auth required
  @Post('submit')
  @UseGuards(DomainRestrictionGuard)
  @AllowedDomains(getDomainConfig().allowedDomains)
  @HttpCode(HttpStatus.CREATED)
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async submitContactForm(
    @Body() createContactDto: CreateContactSubmissionDto,
    @Req() request: Request
  ): Promise<{
    success: boolean;
    message: string;
    data: ContactSubmissionResponseDto;
  }> {
    this.logger.log(`Contact form submission from: ${createContactDto.email}`);

    const domainInfo = (request as any).domainInfo;
    const submission = await this.contactService.createSubmission(createContactDto, domainInfo);

    return {
      success: true,
      message: 'Thank you for your message! We will get back to you soon.',
      data: submission
    };
  }

  // Admin endpoints - Require authentication
  @Get('submissions')
  @UseGuards(JwtAuthGuard)
  async getAllSubmissions(
    @Query('page') page: string = '1',
    @Query('limit') limit: string = '20',
    @Query('status') status?: string,
    @Query('includeSpam') includeSpam: string = 'false'
  ): Promise<{
    success: boolean;
    data: {
      submissions: ContactSubmissionResponseDto[];
      total: number;
      page: number;
      totalPages: number;
    };
  }> {
    const pageNum = parseInt(page, 10) || 1;
    const limitNum = parseInt(limit, 10) || 20;
    const includeSpamBool = includeSpam.toLowerCase() === 'true';

    const result = await this.contactService.getAllSubmissions(
      pageNum,
      limitNum,
      status as any,
      includeSpamBool
    );

    return {
      success: true,
      data: result
    };
  }

  @Get('submissions/stats')
  @UseGuards(JwtAuthGuard)
  async getSubmissionStats(): Promise<{
    success: boolean;
    data: {
      total: number;
      new: number;
      read: number;
      replied: number;
      closed: number;
      spam: number;
    };
  }> {
    const stats = await this.contactService.getSubmissionStats();

    return {
      success: true,
      data: stats
    };
  }

  @Get('submissions/:id')
  @UseGuards(JwtAuthGuard)
  async getSubmissionById(
    @Param('id') id: string
  ): Promise<{
    success: boolean;
    data: ContactSubmissionResponseDto;
  }> {
    const submission = await this.contactService.getSubmissionById(id);

    return {
      success: true,
      data: submission
    };
  }

  @Put('submissions/:id/status')
  @UseGuards(JwtAuthGuard)
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async updateSubmissionStatus(
    @Param('id') id: string,
    @Body() updateStatusDto: UpdateContactSubmissionStatusDto
  ): Promise<{
    success: boolean;
    message: string;
    data: ContactSubmissionResponseDto;
  }> {
    const submission = await this.contactService.updateSubmissionStatus(id, updateStatusDto);

    return {
      success: true,
      message: 'Submission status updated successfully',
      data: submission
    };
  }

  @Put('submissions/:id/spam')
  @UseGuards(JwtAuthGuard)
  async markAsSpam(
    @Param('id') id: string
  ): Promise<{
    success: boolean;
    message: string;
    data: ContactSubmissionResponseDto;
  }> {
    const submission = await this.contactService.markAsSpam(id);

    return {
      success: true,
      message: 'Submission marked as spam',
      data: submission
    };
  }

  @Delete('submissions/:id')
  @UseGuards(JwtAuthGuard)
  async deleteSubmission(
    @Param('id') id: string
  ): Promise<{
    success: boolean;
    message: string;
  }> {
    const result = await this.contactService.deleteSubmission(id);

    return {
      success: true,
      message: result.message
    };
  }

  // Health check endpoint for the contact form
  @Get('health')
  @UseGuards(DomainRestrictionGuard)
  @AllowedDomains(getDomainConfig().allowedDomains)
  async healthCheck(): Promise<{
    success: boolean;
    message: string;
    timestamp: string;
  }> {
    return {
      success: true,
      message: 'Contact form service is healthy',
      timestamp: new Date().toISOString()
    };
  }
}
