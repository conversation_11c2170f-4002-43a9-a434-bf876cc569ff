import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Role } from '../database/models/role.model';
import { RoleResponseDto, RoleListResponseDto } from './dto/role.dto';

@Injectable()
export class RolesService {
  constructor(
    @InjectModel(Role)
    private readonly roleModel: typeof Role,
  ) {}

  /**
   * Get all active roles
   */
  async getRoles(): Promise<RoleListResponseDto> {
    const roles = await this.roleModel.findAll({
      where: { 
        isDeleted: false,
        isActive: true 
      },
      order: [['name', 'ASC']],
    });

    const simplifiedRoles = roles.map(role => role.get({ plain: true }));
    const roleResponses = simplifiedRoles.map((role: any) => this.mapToResponseDto(role));

    return {
      roles: roleResponses,
      total: roleResponses.length,
    };
  }

  /**
   * Map Role model to response DTO
   */
  private mapToResponseDto(role: any): RoleResponseDto {
    return {
      id: role.id,
      name: role.name,
      description: role.description,
      isActive: role.isActive,
      createdAt: role.createdAt,
      updatedAt: role.updatedAt,
    };
  }
}
