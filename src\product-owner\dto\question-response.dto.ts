export class QuestionOptionResponseDto {
  id: string;
  question_id: string;
  option_text: string;
  option_value: number;
  category_id: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export class QuestionResponseDto {
  id: string;
  question_text: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  updatedBy: string;
  options: QuestionOptionResponseDto[];
}

export class QuestionListResponseDto {
  questions: QuestionResponseDto[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}
