import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Sequelize } from 'sequelize-typescript';
import { Question } from '../database/models/question.model';
import { QuestionOption } from '../database/models/question-option.model';
import { QuestionResponseDto, RandomQuestionsResponseDto } from './dto/question.dto';
import { TranslationService } from '../common/services/translation.service';

@Injectable()
export class QuestionsService {
  constructor(
    @InjectModel(Question)
    private readonly questionModel: typeof Question,
    @InjectModel(QuestionOption)
    private readonly questionOptionModel: typeof QuestionOption,
    private readonly sequelize: Sequelize,
    private readonly translationService: TranslationService,
  ) { }



  async getRandomQuestionsForCompany(languageCode: string = 'en'): Promise<RandomQuestionsResponseDto> {
    try {
      // First, get 2 random question IDs using raw SQL
      console.log("Starting to fetch random questions...");

      // First, let's check if there are any questions at all
      const [countResult] = await this.sequelize.query(`
        SELECT COUNT(*) as total FROM Questions WHERE isDeleted = 0 AND isActive = 1
      `);
      console.log("Total active questions:", countResult);

      const [results] = await this.sequelize.query(`
        SELECT TOP 2 q.id
        FROM Questions q
        WHERE q.isDeleted = 0 AND q.isActive = 1
        ORDER BY NEWID()
      `);

      console.log("results", results)

    // Extract question IDs from results
    const questionIds = (results as any[]).map(q => q.id);
    console.log("questionIds", questionIds)

    // If no questions found, return empty response
    if (questionIds.length === 0) {
      return {
        questions: [],
        total: 0
      };
    }

    // Fetch full question data with options using the random IDs
    const questionsWithOptions = await this.questionModel.findAll({
      where: { id: questionIds },
      include: [
        {
          model: QuestionOption,
          as: 'options',
          where: { isDeleted: false },
          required: false,
          order: [['option_value', 'ASC']],
        },
      ]
      // Remove the FIELD() ordering since it's MySQL-specific and we're using SQL Server
      // The random order doesn't matter much for the final response
    });

    console.log("questionsWithOptions", questionsWithOptions)

    // Convert to plain objects and localize
    const simplifiedQuestions = questionsWithOptions.map(question => question.get({ plain: true }));
    console.log("simplifiedQuestions", simplifiedQuestions)

    // Localize questions
    const localizedQuestions = await this.localizeQuestions(simplifiedQuestions, languageCode);

    const questionResponses = localizedQuestions.map((question: any) =>
      this.mapToResponseDto(question)
    );

    console.log("questionResponses", questionResponses)

    

    return {
      questions: questionResponses,
      total: questionResponses.length
    };
    } catch (error) {
      console.error("Error in getRandomQuestionsForCompany:", error);
      throw error;
    }
  }

  /**
   * Map Question model to response DTO
   */
  private mapToResponseDto(question: any): QuestionResponseDto {
    return {
      id: question.id,
      question_text: question.question_text,
      question_type: question.question_type,
      sort_order: question.sort_order,
      isActive: question.isActive,
      options: question.options ? question.options.map((option: any) => ({
        id: option.id,
        option_text: option.option_text,
        option_value: option.option_value,
        sort_order: option.sort_order,
      })) : [],
    };
  }

  /**
   * Localize questions array
   */
  private async localizeQuestions(questions: any[], languageCode: string): Promise<any[]> {
    const localizedQuestions: any[] = [];

    for (const question of questions) {
      const localizedQuestion = await this.localizeQuestion(question, languageCode);
      localizedQuestions.push(localizedQuestion);
    }

    return localizedQuestions;
  }

  /**
   * Localize a single question
   */
  private async localizeQuestion(question: any, languageCode: string): Promise<any> {
    // Get localized question text
    const localizedQuestionText = await this.translationService.getLocalizedQuestionText(
      question.id,
      languageCode,
      question.question_text
    );

    // Get localized options
    const localizedOptions: any[] = [];
    if (question.options && question.options.length > 0) {
      for (const option of question.options) {
        const localizedOptionText = await this.translationService.getLocalizedOptionText(
          option.id,
          languageCode,
          option.option_text
        );

        // Create a new option object with localized text
        const localizedOption = {
          ...option,
          option_text: localizedOptionText
        };
        localizedOptions.push(localizedOption);
      }
    }

    // Create a new question object with localized text and options
    return {
      ...question,
      question_text: localizedQuestionText,
      options: localizedOptions
    };
  }
}
