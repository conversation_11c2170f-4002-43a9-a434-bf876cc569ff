export interface ContactConfig {
  allowedDomains: string[];
  rateLimit: {
    windowMs: number;
    max: number;
  };
  spam: {
    enabled: boolean;
    keywordThreshold: number;
    linkThreshold: number;
    duplicateThreshold: number;
    duplicateWindowMs: number;
  };
  notifications: {
    enabled: boolean;
    email?: string;
    webhook?: string;
  };
}

export const getContactConfig = (): ContactConfig => {
  const environment = process.env.NODE_ENV || 'development';
  
  const baseConfig: ContactConfig = {
    allowedDomains: [],
    rateLimit: {
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 5 // 5 submissions per window
    },
    spam: {
      enabled: true,
      keywordThreshold: 3,
      linkThreshold: 3,
      duplicateThreshold: 3,
      duplicateWindowMs: 60 * 60 * 1000 // 1 hour
    },
    notifications: {
      enabled: false
    }
  };

  switch (environment) {
    case 'production':
      return {
        ...baseConfig,
        allowedDomains: [
          'yourdomain.com',
          'www.yourdomain.com',
          '*.yourdomain.com'
        ],
        notifications: {
          enabled: true,
          email: process.env.CONTACT_NOTIFICATION_EMAIL,
          webhook: process.env.CONTACT_WEBHOOK_URL
        }
      };
      
    case 'staging':
      return {
        ...baseConfig,
        allowedDomains: [
          'staging.yourdomain.com',
          'test.yourdomain.com',
          'yourdomain.com'
        ],
        rateLimit: {
          windowMs: 10 * 60 * 1000, // 10 minutes
          max: 10 // More lenient for testing
        }
      };
      
    default: // development
      return {
        ...baseConfig,
        allowedDomains: [
          'localhost',
          '127.0.0.1',
          'localhost:3000',
          'localhost:3001',
          'localhost:4200',
          '*.localhost'
        ],
        rateLimit: {
          windowMs: 5 * 60 * 1000, // 5 minutes
          max: 20 // Very lenient for development
        },
        spam: {
          ...baseConfig.spam,
          enabled: false // Disable spam detection in development
        }
      };
  }
};

// Environment variables for contact configuration
export const contactEnvConfig = {
  // Domain configuration
  ALLOWED_DOMAINS: process.env.CONTACT_ALLOWED_DOMAINS?.split(',') || [],
  
  // Rate limiting
  RATE_LIMIT_WINDOW_MS: parseInt(process.env.CONTACT_RATE_LIMIT_WINDOW_MS || '900000'), // 15 minutes
  RATE_LIMIT_MAX: parseInt(process.env.CONTACT_RATE_LIMIT_MAX || '5'),
  
  // Spam detection
  SPAM_DETECTION_ENABLED: process.env.CONTACT_SPAM_DETECTION_ENABLED === 'true',
  SPAM_KEYWORD_THRESHOLD: parseInt(process.env.CONTACT_SPAM_KEYWORD_THRESHOLD || '3'),
  SPAM_LINK_THRESHOLD: parseInt(process.env.CONTACT_SPAM_LINK_THRESHOLD || '3'),
  
  // Notifications
  NOTIFICATION_EMAIL: process.env.CONTACT_NOTIFICATION_EMAIL,
  WEBHOOK_URL: process.env.CONTACT_WEBHOOK_URL,
  
  // Database
  CLEANUP_INTERVAL_DAYS: parseInt(process.env.CONTACT_CLEANUP_INTERVAL_DAYS || '90'),
  
  // Security
  ENABLE_HONEYPOT: process.env.CONTACT_ENABLE_HONEYPOT === 'true',
  ENABLE_CAPTCHA: process.env.CONTACT_ENABLE_CAPTCHA === 'true',
  CAPTCHA_SECRET_KEY: process.env.CONTACT_CAPTCHA_SECRET_KEY
};
