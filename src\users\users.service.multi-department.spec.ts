import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/sequelize';
import { UsersService } from './users.service';
import { User } from '../database/models/user.model';
import { Role } from '../database/models/role.model';
import { Company } from '../database/models/company.model';
import { Department } from '../database/models/department.model';
import { SurveyResponse } from '../database/models/survey-response.model';
import { UserDepartment } from '../database/models/user-department.model';
import { EmailService } from '../common/services/email.service';
import { SurveyService } from '../survey/survey.service';
import { BadRequestException } from '@nestjs/common';

describe('UsersService - Multi-Department Support', () => {
  let service: UsersService;
  let userModel: typeof User;
  let departmentModel: typeof Department;
  let userDepartmentModel: typeof UserDepartment;

  const mockCompany = {
    id: 'company-123',
    name: 'Test Company',
    isDeleted: false,
  };

  const mockRole = {
    id: 'role-123',
    name: 'CompanyAdministrator',
    isDeleted: false,
    get: jest.fn().mockReturnValue({
      id: 'role-123',
      name: 'CompanyAdministrator',
    }),
  };

  const mockDepartments = [
    {
      id: 'dept-1',
      name: 'IT Department',
      company_id: 'company-123',
      isDeleted: false,
    },
    {
      id: 'dept-2',
      name: 'HR Department',
      company_id: 'company-123',
      isDeleted: false,
    },
  ];

  const mockUser = {
    id: 'user-123',
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    roleId: 'role-123',
    company_id: 'company-123',
    isActive: true,
    isTemporaryPassword: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockModels = {
    userModel: {
      findOne: jest.fn(),
      create: jest.fn(),
    },
    roleModel: {
      findOne: jest.fn(),
    },
    companyModel: {
      findOne: jest.fn(),
    },
    departmentModel: {
      findAll: jest.fn(),
    },
    userDepartmentModel: {
      create: jest.fn(),
    },
    surveyResponseModel: {},
  };

  const mockEmailService = {
    generateTemporaryPassword: jest.fn().mockReturnValue('temp123'),
    sendWelcomeEmail: jest.fn().mockResolvedValue(true),
  };

  const mockSurveyService = {};

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UsersService,
        {
          provide: getModelToken(User),
          useValue: mockModels.userModel,
        },
        {
          provide: getModelToken(Role),
          useValue: mockModels.roleModel,
        },
        {
          provide: getModelToken(Company),
          useValue: mockModels.companyModel,
        },
        {
          provide: getModelToken(Department),
          useValue: mockModels.departmentModel,
        },
        {
          provide: getModelToken(SurveyResponse),
          useValue: mockModels.surveyResponseModel,
        },
        {
          provide: getModelToken(UserDepartment),
          useValue: mockModels.userDepartmentModel,
        },
        {
          provide: EmailService,
          useValue: mockEmailService,
        },
        {
          provide: SurveyService,
          useValue: mockSurveyService,
        },
      ],
    }).compile();

    service = module.get<UsersService>(UsersService);
    userModel = module.get<typeof User>(getModelToken(User));
    departmentModel = module.get<typeof Department>(getModelToken(Department));
    userDepartmentModel = module.get<typeof UserDepartment>(getModelToken(UserDepartment));
  });

  describe('createUser - CompanyAdministrator with multiple departments', () => {
    it('should create CompanyAdministrator with multiple departments', async () => {
      // Setup mocks
      mockModels.companyModel.findOne.mockResolvedValue(mockCompany);
      mockModels.userModel.findOne.mockResolvedValue(null); // No existing user
      mockModels.roleModel.findOne.mockResolvedValue(mockRole);
      mockModels.departmentModel.findAll.mockResolvedValue(mockDepartments);
      mockModels.userModel.create.mockResolvedValue(mockUser);
      mockModels.userDepartmentModel.create.mockResolvedValue({});

      const createUserDto = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        role: 'CompanyAdministrator',
        departmentIds: ['dept-1', 'dept-2'],
      };

      const result = await service.createUser('company-123', createUserDto, 'creator-123');

      // Verify user creation
      expect(mockModels.userModel.create).toHaveBeenCalledWith(
        expect.objectContaining({
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          roleId: 'role-123',
          company_id: 'company-123',
        })
      );

      // Verify UserDepartment relationships created
      expect(mockModels.userDepartmentModel.create).toHaveBeenCalledTimes(2);
      expect(mockModels.userDepartmentModel.create).toHaveBeenCalledWith(
        expect.objectContaining({
          user_id: 'user-123',
          department_id: 'dept-1',
          isActive: true,
          createdBy: 'creator-123',
        })
      );
      expect(mockModels.userDepartmentModel.create).toHaveBeenCalledWith(
        expect.objectContaining({
          user_id: 'user-123',
          department_id: 'dept-2',
          isActive: true,
          createdBy: 'creator-123',
        })
      );
    });

    it('should throw error if CompanyAdministrator has no departments', async () => {
      mockModels.companyModel.findOne.mockResolvedValue(mockCompany);
      mockModels.userModel.findOne.mockResolvedValue(null);
      mockModels.roleModel.findOne.mockResolvedValue(mockRole);

      const createUserDto = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        role: 'CompanyAdministrator',
        departmentIds: [],
      };

      await expect(
        service.createUser('company-123', createUserDto, 'creator-123')
      ).rejects.toThrow('CompanyAdministrator must be assigned to at least one department');
    });

    it('should throw error if departments not found', async () => {
      mockModels.companyModel.findOne.mockResolvedValue(mockCompany);
      mockModels.userModel.findOne.mockResolvedValue(null);
      mockModels.roleModel.findOne.mockResolvedValue(mockRole);
      mockModels.departmentModel.findAll.mockResolvedValue([mockDepartments[0]]); // Only one department found

      const createUserDto = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        role: 'CompanyAdministrator',
        departmentIds: ['dept-1', 'dept-2'], // Two departments requested
      };

      await expect(
        service.createUser('company-123', createUserDto, 'creator-123')
      ).rejects.toThrow('One or more departments not found or do not belong to your company');
    });
  });

  describe('createUser - Single department roles', () => {
    it('should create Employee with single department', async () => {
      const employeeRole = {
        id: 'role-employee',
        name: 'Employee',
        isDeleted: false,
        get: jest.fn().mockReturnValue({
          id: 'role-employee',
          name: 'Employee',
        }),
      };

      mockModels.companyModel.findOne.mockResolvedValue(mockCompany);
      mockModels.userModel.findOne.mockResolvedValue(null);
      mockModels.roleModel.findOne.mockResolvedValue(employeeRole);
      mockModels.departmentModel.findOne.mockResolvedValue(mockDepartments[0]);
      mockModels.userModel.create.mockResolvedValue(mockUser);
      mockModels.userDepartmentModel.create.mockResolvedValue({});

      const createUserDto = {
        firstName: 'Jane',
        lastName: 'Smith',
        email: '<EMAIL>',
        role: 'Employee',
        departmentId: 'dept-1',
      };

      await service.createUser('company-123', createUserDto, 'creator-123');

      // Verify single UserDepartment relationship created
      expect(mockModels.userDepartmentModel.create).toHaveBeenCalledTimes(1);
      expect(mockModels.userDepartmentModel.create).toHaveBeenCalledWith(
        expect.objectContaining({
          user_id: 'user-123',
          department_id: 'dept-1',
          isActive: true,
          createdBy: 'creator-123',
        })
      );
    });

    it('should throw error if Employee has no department', async () => {
      const employeeRole = {
        id: 'role-employee',
        name: 'Employee',
        isDeleted: false,
        get: jest.fn().mockReturnValue({
          id: 'role-employee',
          name: 'Employee',
        }),
      };

      mockModels.companyModel.findOne.mockResolvedValue(mockCompany);
      mockModels.userModel.findOne.mockResolvedValue(null);
      mockModels.roleModel.findOne.mockResolvedValue(employeeRole);

      const createUserDto = {
        firstName: 'Jane',
        lastName: 'Smith',
        email: '<EMAIL>',
        role: 'Employee',
        // No departmentId provided
      };

      await expect(
        service.createUser('company-123', createUserDto, 'creator-123')
      ).rejects.toThrow('Employee must be assigned to a department');
    });
  });
});
