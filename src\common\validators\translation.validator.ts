import { registerDecorator, ValidationOptions, ValidationArguments } from 'class-validator';

export interface TranslationObject {
  [languageCode: string]: string;
}

export function IsTranslationOrString(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: 'isTranslationOrString',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          // Allow string for backward compatibility
          if (typeof value === 'string' && value.trim().length > 0) {
            return true;
          }

          // Allow translation object
          if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
            const translations = value as TranslationObject;
            const keys = Object.keys(translations);
            
            // Must have at least one translation
            if (keys.length === 0) {
              return false;
            }

            // All values must be non-empty strings
            for (const key of keys) {
              if (typeof translations[key] !== 'string' || translations[key].trim().length === 0) {
                return false;
              }
            }

            // Must have English (default language) translation
            if (!translations.en || translations.en.trim().length === 0) {
              return false;
            }

            return true;
          }

          return false;
        },
        defaultMessage(args: ValidationArguments) {
          return `${args.property} must be either a non-empty string or a translation object with at least English (en) translation`;
        },
      },
    });
  };
}
