{
  "scripts": {
    "analyze:models": "node scripts/analyze-models.js",
    "generate:migrations-from-models": "node scripts/generate-migrations-from-models.js",
    "db:migrate:sequential": "node scripts/run-migrations-sequential.js up",
    "db:migrate:sequential:status": "node scripts/run-migrations-sequential.js status",
    "db:migrate:sequential:rollback": "node scripts/run-migrations-sequential.js down",
    "db:migrate:sequential:help": "node scripts/run-migrations-sequential.js help",
    "db:setup:from-models": "npm run generate:migrations-from-models && npm run db:migrate:sequential"
  }
}

// Add these scripts to your main package.json file
