import { Table, Column, Model, DataType, <PERSON><PERSON>ey, Default, ForeignKey, BelongsTo, AllowNull } from 'sequelize-typescript';
import { Company } from '../database/models/company.model';
import { Role } from '../database/models/role.model';

@Table({
  tableName: 'Users',
  timestamps: false,
})
export class User extends Model<User> {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column({ type: DataType.UUID })
  declare id: string;

  @Column({ type: DataType.STRING(150), allowNull: false })
  declare firstName: string;

  @Column({ type: DataType.STRING(150), allowNull: false })
  declare lastName: string;

  @Column({ type: DataType.STRING(150), allowNull: false, unique: true })
  declare email: string;

  @Column({ type: DataType.STRING(255), allowNull: false })
  declare password: string;

  @ForeignKey(() => Role)
  @AllowNull(false)
  @Column(DataType.UUID)
  declare roleId: string;

  @ForeignKey(() => Company)
  @Column({ type: DataType.UUID, allowNull: true })
  declare company_id: string;

  @Column({ type: DataType.UUID, allowNull: true })
  declare supervisor_id: string;

  @Default(true)
  @Column({ type: DataType.BOOLEAN })
  declare isActive: boolean;

  @Default(false)
  @Column({ type: DataType.BOOLEAN })
  declare isDeleted: boolean;

  @Column({ type: DataType.DATE })
  declare createdAt: Date;

  @Column({ type: DataType.UUID, allowNull: true })
  declare createdBy: string;

  @Column({ type: DataType.DATE })
  declare updatedAt: Date;

  @Column({ type: DataType.UUID, allowNull: true })
  declare updatedBy: string;

  @Column({ type: DataType.UUID, allowNull: true })
  declare deletedBy: string;

  @BelongsTo(() => Company)
  company: Company;

  @BelongsTo(() => Role)
  role: Role;
}
