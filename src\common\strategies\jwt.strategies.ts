// src/core/strategies/jwt.strategy.ts
import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { jwtConstants } from '../constants/jwt.constants';
import { JwtPayload, CurrentUser } from '../../auth/types/current-user.interface';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor() {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false, // We'll handle infinite tokens in the validate method
      secretOrKey: jwtConstants.secret,
    });
  }

  async validate(payload: JwtPayload): Promise<CurrentUser> {
    // Add detailed logging here
    console.log('JWT Payload:', JSON.stringify(payload, null, 2));

    // Check if this is an infinite token (no exp field means infinite)
    const isInfiniteToken = !payload.exp;
    if (isInfiniteToken && payload.role === 'CompanyAdmin') {
      console.log('🔓 Infinite token validated for CompanyAdmin:', payload.email);
    }

    return {
      sub: payload.sub,
      userId: payload.sub,
      email: payload.email,
      role: payload.role,
      companyId: payload.companyId,
      department_id: payload.department_id || null,
      isInfiniteToken
    };
  }
}
