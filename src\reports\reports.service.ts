import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Sequelize } from 'sequelize-typescript';
import { Op, QueryTypes, CreationAttributes } from 'sequelize';
import { SurveyResponse } from '../database/models/survey-response.model';
import { AnswerCategory } from '../database/models/answer-category.model';
import { QuestionOption } from '../database/models/question-option.model';
import { Question } from '../database/models/question.model';
import { User } from '../database/models/user.model';
import { Company } from '../database/models/company.model';
import { Department } from '../database/models/department.model';
import { Report, ReportType, ReportStatus, ReportFormat } from '../database/models/report.model';
import { CreateReportDto, UpdateReportDto, ReportQueryDto, GenerateReportDto } from './dto/report.dto';

@Injectable()
export class ReportsService {
  constructor(
    @InjectModel(SurveyResponse)
    private readonly surveyResponseModel: typeof SurveyResponse,
    @InjectModel(AnswerCategory)
    private readonly answerCategoryModel: typeof AnswerCategory,
    @InjectModel(QuestionOption)
    private readonly questionOptionModel: typeof QuestionOption,
    @InjectModel(Question)
    private readonly questionModel: typeof Question,
    @InjectModel(User)
    private readonly userModel: typeof User,
    @InjectModel(Company)
    private readonly companyModel: typeof Company,
    @InjectModel(Department)
    private readonly departmentModel: typeof Department,
    @InjectModel(Report)
    private readonly reportModel: typeof Report,
    private readonly sequelize: Sequelize,
  ) { }

  /**
   * Get unified dashboard data based on user role
   */
  async getDashboardData(
    user: any,
    startDate: Date,
    endDate: Date,
    departmentId?: string,
    period: 'daily' | 'weekly' | 'monthly' = 'weekly'
  ) {
    // Get accessible user IDs based on role
    const accessibleUserIds = await this.getAccessibleUserIds(user, departmentId);

    console.log("accessibleUserIds", accessibleUserIds);

    if (accessibleUserIds.length === 0) {
      return this.getEmptyDashboard(user);
    }

    // Build base query with role restrictions
    const baseWhereClause = {
      user_id: { [Op.in]: accessibleUserIds },
      response_date: { [Op.between]: [startDate, endDate] },
      isActive: true,
      isDeleted: false
    };

    // Fetch all dashboard data in parallel
    const [
      summary,
      categoryDistribution,
      departmentAnalytics,
      recentActivity,
      trendAnalysis,
    ] = await Promise.all([
      this.getDashboardSummary(baseWhereClause, accessibleUserIds),
      this.getDashboardCategoryDistribution(baseWhereClause, user),
      this.getDashboardDepartmentAnalytics(baseWhereClause, user),
      this.getDashboardRecentActivity(baseWhereClause),
      this.getDashboardTrendAnalysis(baseWhereClause, period),
    ]);

    return {
      summary,
      categoryDistribution,
      trendAnalysis,
      departmentAnalytics,
      recentActivity,
      availableFilters: await this.getAvailableFilters(user),
      userAccess: this.getUserAccessInfo(user)
    };
  }

  /**
   * Get comprehensive survey analytics for a company
   */
  async getCompanySurveyAnalytics(
    companyId: string,
    startDate: Date,
    endDate: Date,
    departmentFilter?: string
  ) {
    // Get company info
    const company = await this.companyModel.findByPk(companyId);
    if (!company) {
      throw new Error('Company not found');
    }

    // Build where clause for responses
    const whereClause: any = {
      company_id: companyId,
      response_date: { [Op.between]: [startDate, endDate] },
      isActive: true,
      isDeleted: false
    };

    if (departmentFilter) {
      whereClause.department = departmentFilter;
    }

    // Get total responses and response rate
    const totalResponses = await this.surveyResponseModel.count({ where: whereClause });
    const totalEmployees = await this.userModel.count({
      where: { company_id: companyId, isActive: true, isDeleted: false }
    });

    // Get category breakdown
    const categoryBreakdown = await this.getCategoryBreakdown(whereClause);

    // Get time series data
    const timeSeriesData = await this.getTimeSeriesData(companyId, startDate, endDate);

    // Get department breakdown
    const departmentBreakdown = await this.getDepartmentBreakdownData(companyId, startDate, endDate);

    // Get top questions analysis
    const topQuestions = await this.getTopQuestionsAnalysis(companyId, startDate, endDate);

    return {
      companyId,
      companyName: company.name,
      reportPeriod: { startDate, endDate },
      totalResponses,
      totalEmployees,
      responseRate: totalEmployees > 0 ? (totalResponses / totalEmployees) * 100 : 0,
      categoryBreakdown,
      timeSeriesData,
      departmentBreakdown,
      topQuestions
    };
  }

  /**
   * Get survey analytics for a supervisor's team
   */
  async getSupervisorTeamAnalytics(
    supervisorId: string,
    startDate: Date,
    endDate: Date
  ) {
    const whereClause = {
      supervisor_id: supervisorId,
      response_date: { [Op.between]: [startDate, endDate] },
      isActive: true,
      isDeleted: false
    };

    const totalResponses = await this.surveyResponseModel.count({ where: whereClause });
    const teamSize = await this.userModel.count({
      where: { supervisor_id: supervisorId, isActive: true, isDeleted: false }
    });

    const categoryBreakdown = await this.getCategoryBreakdown(whereClause);

    return {
      supervisorId,
      reportPeriod: { startDate, endDate },
      totalResponses,
      teamSize,
      responseRate: teamSize > 0 ? (totalResponses / teamSize) * 100 : 0,
      categoryBreakdown
    };
  }

  /**
   * Get category breakdown for responses
   */
  private async getCategoryBreakdown(whereClause: any) {
    const results = await this.surveyResponseModel.findAll({
      where: whereClause,
      include: [
        {
          model: QuestionOption,
          as: 'selectedOption',
          include: [
            {
              model: AnswerCategory,
              as: 'category'
            }
          ]
        }
      ]
    });

    const categoryStats = new Map();
    const totalResponses = results.length;

    // Initialize all categories
    const allCategories = await this.answerCategoryModel.findAll({
      where: { isActive: true, isDeleted: false },
      order: [['sort_order', 'ASC']]
    });

    allCategories.forEach(category => {
      categoryStats.set(category.id, {
        categoryId: category.id,
        categoryName: category.name,
        displayName: category.display_name,
        colorCode: category.color_code,
        responseCount: 0,
        percentage: 0,
        trend: 0 // TODO: Calculate trend
      });
    });

    // Count responses by category
    results.forEach(response => {
      const categoryId = response.selectedOption?.category?.id;
      if (categoryId && categoryStats.has(categoryId)) {
        const stats = categoryStats.get(categoryId);
        stats.responseCount++;
        stats.percentage = totalResponses > 0 ? (stats.responseCount / totalResponses) * 100 : 0;
      }
    });

    return Array.from(categoryStats.values());
  }

  /**
   * Get time series data for trend analysis
   */
  private async getTimeSeriesData(companyId: string, startDate: Date, endDate: Date) {
    // This is a simplified version - you might want to group by week/month
    const query = `
      SELECT 
        DATE(response_date) as date,
        ac.name as category,
        COUNT(*) as count
      FROM SurveyResponses sr
      JOIN QuestionOptions qo ON sr.selected_option_id = qo.id
      JOIN AnswerCategories ac ON qo.category_id = ac.id
      WHERE sr.company_id = :companyId 
        AND sr.response_date BETWEEN :startDate AND :endDate
        AND sr.isActive = 1 
        AND sr.isDeleted = 0
      GROUP BY DATE(response_date), ac.name, ac.sort_order
      ORDER BY DATE(response_date), ac.sort_order
    `;

    const results = await this.sequelize.query(query, {
      replacements: { companyId, startDate, endDate },
      type: QueryTypes.SELECT
    });

    // Transform results into time series format
    const timeSeriesMap = new Map();

    results.forEach((row: any) => {
      if (!timeSeriesMap.has(row.date)) {
        timeSeriesMap.set(row.date, {
          date: row.date,
          VERY_POSITIVE: 0,
          POSITIVE: 0,
          NEUTRAL: 0,
          NEGATIVE: 0,
          VERY_NEGATIVE: 0
        });
      }
      timeSeriesMap.get(row.date)[row.category] = row.count;
    });

    return Array.from(timeSeriesMap.values());
  }

  /**
   * Get department breakdown data
   */
  private async getDepartmentBreakdownData(companyId: string, startDate: Date, endDate: Date) {
    const query = `
      SELECT 
        sr.department,
        ac.name as category,
        COUNT(*) as count
      FROM SurveyResponses sr
      JOIN QuestionOptions qo ON sr.selected_option_id = qo.id
      JOIN AnswerCategories ac ON qo.category_id = ac.id
      WHERE sr.company_id = :companyId 
        AND sr.response_date BETWEEN :startDate AND :endDate
        AND sr.isActive = 1 
        AND sr.isDeleted = 0
        AND sr.department IS NOT NULL
      GROUP BY sr.department, ac.name
      ORDER BY sr.department, ac.sort_order
    `;

    const results = await this.sequelize.query(query, {
      replacements: { companyId, startDate, endDate },
      type: QueryTypes.SELECT
    });

    // Transform results
    const departmentMap = new Map();

    results.forEach((row: any) => {
      if (!departmentMap.has(row.department)) {
        departmentMap.set(row.department, {
          departmentName: row.department,
          totalResponses: 0,
          categories: {
            VERY_POSITIVE: 0,
            POSITIVE: 0,
            NEUTRAL: 0,
            NEGATIVE: 0,
            VERY_NEGATIVE: 0
          }
        });
      }
      const dept = departmentMap.get(row.department);
      dept.categories[row.category] = row.count;
      dept.totalResponses += row.count;
    });

    return Array.from(departmentMap.values());
  }

  /**
   * Get top questions analysis
   */
  private async getTopQuestionsAnalysis(companyId: string, startDate: Date, endDate: Date) {
    const query = `
      SELECT 
        q.id as questionId,
        q.question_text,
        ac.name as category,
        COUNT(*) as count
      FROM SurveyResponses sr
      JOIN Questions q ON sr.question_id = q.id
      JOIN QuestionOptions qo ON sr.selected_option_id = qo.id
      JOIN AnswerCategories ac ON qo.category_id = ac.id
      WHERE sr.company_id = :companyId 
        AND sr.response_date BETWEEN :startDate AND :endDate
        AND sr.isActive = 1 
        AND sr.isDeleted = 0
      GROUP BY q.id, q.question_text, ac.name
      ORDER BY q.id, ac.sort_order
    `;

    const results = await this.sequelize.query(query, {
      replacements: { companyId, startDate, endDate },
      type: QueryTypes.SELECT
    });

    // Transform results
    const questionMap = new Map();

    results.forEach((row: any) => {
      if (!questionMap.has(row.questionId)) {
        questionMap.set(row.questionId, {
          questionId: row.questionId,
          questionText: row.question_text,
          totalResponses: 0,
          categoryDistribution: {
            VERY_POSITIVE: 0,
            POSITIVE: 0,
            NEUTRAL: 0,
            NEGATIVE: 0,
            VERY_NEGATIVE: 0
          }
        });
      }
      const question = questionMap.get(row.questionId);
      question.categoryDistribution[row.category] = row.count;
      question.totalResponses += row.count;
    });

    return Array.from(questionMap.values()).slice(0, 10); // Top 10 questions
  }

  // Additional methods for other endpoints...
  async getQuestionCategoryBreakdown(questionId: string, companyId?: string, startDate?: Date, endDate?: Date) {
    // Implementation for question-specific breakdown
    // ... (implement based on requirements)
  }

  async getTrendAnalysis(companyId: string, startDate: Date, endDate: Date, interval: string) {
    // Implementation for trend analysis
    // ... (implement based on requirements)
  }

  async getDepartmentAnalytics(companyId: string, startDate: Date, endDate: Date) {
    // Implementation for department analytics
    // ... (implement based on requirements)
  }

  async exportReportData(companyId: string, format: string, startDate: Date, endDate: Date) {
    // Implementation for export functionality
    // ... (implement based on requirements)
  }

  // Report Management Methods

  /**
   * Create a new report record
   */
  async createReport(createReportDto: CreateReportDto): Promise<Report> {
    const reportData = {
      ...createReportDto,
      start_date: new Date(createReportDto.start_date),
      end_date: new Date(createReportDto.end_date),
      status: ReportStatus.PENDING,
      // Set expiration to 30 days from now by default
      expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
    };

    return this.reportModel.create(reportData as CreationAttributes<Report>);
  }

  /**
   * Get all reports with optional filtering
   */
  async getReports(queryDto: ReportQueryDto): Promise<{ reports: Report[]; total: number }> {
    const whereClause: any = {
      isDeleted: false
    };

    if (queryDto.company_id) {
      whereClause.company_id = queryDto.company_id;
    }

    if (queryDto.type) {
      whereClause.type = queryDto.type;
    }

    if (queryDto.status) {
      whereClause.status = queryDto.status;
    }

    if (queryDto.format) {
      whereClause.format = queryDto.format;
    }

    if (queryDto.requested_by) {
      whereClause.requested_by = queryDto.requested_by;
    }

    if (queryDto.start_date && queryDto.end_date) {
      whereClause.start_date = {
        [Op.between]: [new Date(queryDto.start_date), new Date(queryDto.end_date)]
      };
    }

    const { count, rows } = await this.reportModel.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: Company,
          attributes: ['id', 'name']
        },
        {
          model: User,
          as: 'requestedBy',
          attributes: ['id', 'firstName', 'lastName', 'email']
        }
      ],
      order: [['createdAt', 'DESC']],
      limit: queryDto.limit || 10,
      offset: queryDto.offset || 0
    });

    return {
      reports: rows,
      total: count
    };
  }

  /**
   * Get a specific report by ID
   */
  async getReportById(id: string): Promise<Report> {
    const report = await this.reportModel.findOne({
      where: { id, isDeleted: false },
      include: [
        {
          model: Company,
          attributes: ['id', 'name']
        },
        {
          model: User,
          as: 'requestedBy',
          attributes: ['id', 'firstName', 'lastName', 'email']
        }
      ]
    });

    if (!report) {
      throw new NotFoundException(`Report with ID ${id} not found`);
    }

    return report;
  }

  /**
   * Update a report
   */
  async updateReport(id: string, updateReportDto: UpdateReportDto): Promise<Report> {
    const report = await this.getReportById(id);

    const updateData: any = { ...updateReportDto };

    // Convert date strings to Date objects
    if (updateReportDto.processing_started_at) {
      updateData.processing_started_at = new Date(updateReportDto.processing_started_at);
    }
    if (updateReportDto.processing_completed_at) {
      updateData.processing_completed_at = new Date(updateReportDto.processing_completed_at);
    }
    if (updateReportDto.expires_at) {
      updateData.expires_at = new Date(updateReportDto.expires_at);
    }

    await report.update(updateData);
    return this.getReportById(id);
  }

  /**
   * Delete a report (soft delete)
   */
  async deleteReport(id: string, userId: string): Promise<{ message: string }> {
    const report = await this.getReportById(id);

    await report.update({
      isDeleted: true,
      deletedBy: userId
    });

    return { message: 'Report deleted successfully' };
  }

  /**
   * Generate and store a report
   */
  async generateAndStoreReport(generateReportDto: GenerateReportDto, userId: string): Promise<Report> {
    const transaction = await this.sequelize.transaction();

    try {
      // Create report record
      const reportName = generateReportDto.name ||
        `${generateReportDto.type.replace(/_/g, ' ').toUpperCase()} - ${new Date().toISOString().split('T')[0]}`;

      const report = await this.reportModel.create({
        name: reportName,
        type: generateReportDto.type,
        format: generateReportDto.format || ReportFormat.JSON,
        company_id: generateReportDto.company_id,
        requested_by: userId,
        start_date: new Date(generateReportDto.start_date),
        end_date: new Date(generateReportDto.end_date),
        parameters: generateReportDto.parameters || {},
        status: ReportStatus.PROCESSING,
        processing_started_at: new Date(),
        expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        createdBy: userId
      } as CreationAttributes<Report>, { transaction });

      // Generate the actual report data based on type
      let reportData: any;
      const startDate = new Date(generateReportDto.start_date);
      const endDate = new Date(generateReportDto.end_date);

      try {
        switch (generateReportDto.type) {
          case ReportType.COMPANY_SURVEY_ANALYTICS:
            reportData = await this.getCompanySurveyAnalytics(
              generateReportDto.company_id,
              startDate,
              endDate,
              generateReportDto.parameters?.departmentFilter
            );
            break;

          case ReportType.TREND_ANALYSIS:
            reportData = await this.getTimeSeriesData(
              generateReportDto.company_id,
              startDate,
              endDate
            );
            break;

          case ReportType.DEPARTMENT_ANALYTICS:
            reportData = await this.getDepartmentBreakdownData(
              generateReportDto.company_id,
              startDate,
              endDate
            );
            break;

          default:
            throw new BadRequestException(`Report type ${generateReportDto.type} not implemented`);
        }

        // Update report with generated data
        await report.update({
          data: reportData,
          status: ReportStatus.COMPLETED,
          processing_completed_at: new Date()
        }, { transaction });

      } catch (error) {
        // Update report with error status
        await report.update({
          status: ReportStatus.FAILED,
          error_message: error.message,
          processing_completed_at: new Date()
        }, { transaction });

        throw error;
      }

      await transaction.commit();
      return this.getReportById(report.id);

    } catch (error) {
      await transaction.rollback();
      throw new BadRequestException(`Failed to generate report: ${error.message}`);
    }
  }

  /**
   * Clean up expired reports
   */
  async cleanupExpiredReports(): Promise<{ deletedCount: number }> {
    const expiredReports = await this.reportModel.findAll({
      where: {
        expires_at: {
          [Op.lt]: new Date()
        },
        isDeleted: false
      }
    });

    const deletedCount = expiredReports.length;

    if (deletedCount > 0) {
      await this.reportModel.update(
        { isDeleted: true },
        {
          where: {
            id: {
              [Op.in]: expiredReports.map(report => report.id)
            }
          }
        }
      );
    }

    return { deletedCount };
  }

  // Dashboard Helper Methods

  /**
   * Get accessible user IDs based on user role
   */
  private async getAccessibleUserIds(user: any, departmentId?: string): Promise<string[]> {
    const baseWhere: any = {
      company_id: user.companyId,
      isActive: true,
      isDeleted: false
    };

    if (departmentId) {
      baseWhere.department_id = departmentId;
    }

    console.log("user_in_getAccessibleUserIds", user);
    console.log("departmentId_in_getAccessibleUserIds", departmentId);

    switch (user.role) {
      case 'Supervisor':
        // Supervisors can see their department employees only
        const supervisorUsers = await this.userModel.findAll({
          where: {
            ...baseWhere,
            department_id: user.department_id,
            roleId: { [Op.in]: await this.getRoleIdsByNames(['Employee']) }
          },
          attributes: ['id']
        });
        return supervisorUsers.map(u => u.id);

      case 'CompanyManager':
        // Managers can see employees + supervisors in their department
        const managerUsers = await this.userModel.findAll({
          where: {
            ...baseWhere,
            department_id: user.department_id,
            roleId: { [Op.in]: await this.getRoleIdsByNames(['Employee', 'Supervisor']) }
          },
          attributes: ['id']
        });
        return managerUsers.map(u => u.id);

      case 'CompanyAdmin':
        // CompanyAdmin can see all users except other CompanyAdmins
        const companyUsers = await this.userModel.findAll({
          where: {
            ...baseWhere,
            roleId: { [Op.in]: await this.getRoleIdsByNames(['Employee', 'Supervisor', 'CompanyManager']) }
          },
          attributes: ['id']
        });

        return companyUsers.map(u => u.id);

      default:
        return []; // No access for regular employees
    }
  }

  /**
   * Get role IDs by role names
   */
  private async getRoleIdsByNames(roleNames: string[]): Promise<string[]> {
    const roles = await this.sequelize.query(
      'SELECT id FROM Roles WHERE name IN (:roleNames) AND isDeleted = 0',
      {
        replacements: { roleNames },
        type: QueryTypes.SELECT
      }
    );
    return roles.map((role: any) => role.id);
  }

  /**
   * Get dashboard summary data
   */
  private async getDashboardSummary(whereClause: any, accessibleUserIds: string[]) {
    const totalResponses = await this.surveyResponseModel.count({ where: whereClause });
    console.log("totalResponses", totalResponses);

    const totalEligibleUsers = accessibleUserIds.length;

    const activeUsers = await this.surveyResponseModel.count({
      where: whereClause,
      distinct: true,
      col: 'user_id'
    });


    // Calculate average score based on category values
    const avgScoreResult = await this.sequelize.query(`
      SELECT AVG(CAST(qo.option_value AS FLOAT)) as avgScore
      FROM SurveyResponses sr
      JOIN QuestionOptions qo ON sr.selected_option_id = qo.id
      WHERE sr.user_id IN (:userIds)
        AND sr.response_date BETWEEN :startDate AND :endDate
        AND sr.isActive = 1 AND sr.isDeleted = 0
    `, {
      replacements: {
        userIds: accessibleUserIds,
        startDate: whereClause.response_date[Op.between][0],
        endDate: whereClause.response_date[Op.between][1]
      },
      type: QueryTypes.SELECT
    });


    const averageScore = (avgScoreResult[0] as any)?.avgScore || 0;

    const responseRate = totalEligibleUsers > 0 ? (activeUsers / totalEligibleUsers) * 100 : 0;

    return {
      totalResponses,
      responseRate: Math.round(responseRate * 10) / 10,
      averageScore: Math.round(averageScore * 10) / 10,
      activeUsers,
      totalEligibleUsers,
      lastUpdated: new Date().toISOString()
    };
  }

  /**
   * Get category distribution for dashboard with debugging
   */
  private async getDashboardCategoryDistribution(whereClause: any, user: any) {

    console.log("user", user)
    console.log("whereClause", whereClause)
    try {
      const accessibleUserIds = whereClause.user_id[Op.in];
      const startDate = whereClause.response_date[Op.between][0]
      const endDate = whereClause.response_date[Op.between][1]

      // Build department filter based on user role
      let departmentFilter = '';
      let queryParams: any = {
        userIds: accessibleUserIds,
        startDate,
        endDate
      };

      // Apply role-based filtering
      if (user.role === 'CompanyAdministrator' || user.role === 'CompanyAdmin') {
        // CompanyAdmin and CompanyAdministrator can see all departments
        departmentFilter = '';
      } else if (user.role === 'Supervisor' || user.role === 'CompanyManager' || user.role === 'Employee') {
        // Other roles can only see their own department data
        if (user.department_id) {
          departmentFilter = 'AND u.department_id = :departmentId';
          queryParams.departmentId = user.department_id;
        } else {
          // If user has no department, return empty results
          return {
            totalResponses: 0,
            categories: [],
            chartData: { labels: [], datasets: [] }
          };
        }
      }

      // Updated query with department filtering and proper joins
      const categoryData = await this.sequelize.query(`
      SELECT
        ac.name,
        ac.display_name,
        ac.color_code,
        ac.sort_order,
        COUNT(*) as count
      FROM SurveyResponses sr
      JOIN QuestionOptions qo ON sr.selected_option_id = qo.id
      JOIN AnswerCategories ac ON qo.category_id = ac.id
      JOIN Users u ON sr.user_id = u.id
      WHERE sr.user_id IN (:userIds)
        AND sr.response_date BETWEEN :startDate AND :endDate
        AND sr.isActive = 1
        AND sr.isDeleted = 0
        AND qo.isDeleted = 0
        AND ac.isDeleted = 0
        AND u.isActive = 1
        AND u.isDeleted = 0
        ${departmentFilter}
      GROUP BY ac.id, ac.name, ac.display_name, ac.color_code, ac.sort_order
      ORDER BY ac.sort_order
    `, {
        replacements: queryParams,
        type: QueryTypes.SELECT
      });


      const totalResponses = categoryData.reduce((sum: number, cat: any) => sum + parseInt(cat.count), 0);
      console.log("totalResponses", totalResponses)

      const categories = categoryData.map((cat: any) => ({
        id: cat.name, // Use name as ID for now
        name: cat.name,
        displayName: cat.display_name,
        colorCode: cat.color_code,
        count: parseInt(cat.count),
        percentage: totalResponses > 0 ? Math.round((parseInt(cat.count) / totalResponses) * 1000) / 10 : 0
      }));

      return {
        totalResponses,
        categories,
        chartData: {
          labels: categories.map(c => c.displayName),
          datasets: [{
            data: categories.map(c => c.count),
            backgroundColor: categories.map(c => c.colorCode),
            borderColor: categories.map(c => c.colorCode),
            borderWidth: 1
          }]
        }
      };
    } catch (error) {
      console.error('❌ Error in getDashboardCategoryDistribution:', error);
      // Return empty structure on error
      return {
        totalResponses: 0,
        categories: [],
        chartData: { labels: [], datasets: [] }
      };
    }
  }

  /**
   * Get empty dashboard for users with no access
   */
  private getEmptyDashboard(user: any) {
    return {
      summary: {
        totalResponses: 0,
        responseRate: 0,
        averageScore: 0,
        activeUsers: 0,
        totalEligibleUsers: 0,
        lastUpdated: new Date().toISOString()
      },
      categoryDistribution: {
        totalResponses: 0,
        categories: [],
        chartData: { labels: [], datasets: [{ data: [], backgroundColor: [], borderColor: [] }] }
      },
      trendAnalysis: {
        timeline: [],
        chartData: { labels: [], datasets: [] }
      },
      departmentAnalytics: {
        departments: [],
        responseRateChart: { labels: [], datasets: [] },
        satisfactionChart: { labels: [], datasets: [] }
      },
      recentActivity: {
        responses: [],
        pagination: { currentPage: 1, totalPages: 0, totalItems: 0, itemsPerPage: 10 }
      },
      availableFilters: { departments: [], dateRanges: [], users: [] },
      userAccess: this.getUserAccessInfo(user)
    };
  }

  /**
   * Get user access information
   */
  private getUserAccessInfo(user: any) {
    return {
      role: user.role,
      accessLevel: this.getAccessLevel(user.role),
      canExport: ['CompanyAdmin', 'Manager'].includes(user.role),
      visibleDepartments: user.role === 'CompanyAdmin' ? ['all'] : [user.department_id]
    };
  }

  private getAccessLevel(role: string): string {
    switch (role) {
      case 'Supervisor': return 'department_employees';
      case 'Manager': return 'department_all';
      case 'CompanyAdmin': return 'company_all';
      default: return 'none';
    }
  }

  /**
   * Placeholder methods for remaining dashboard components
   */
  private async getDashboardTrendAnalysis(whereClause: any, period: string) {

    // try {
    //   // Determine date grouping based on period
    //   let dateFormat: string;
    //   let dateGroupBy: string;

    //   switch (period) {
    //     case 'daily':
    //       dateFormat = '%Y-%m-%d';
    //       dateGroupBy = 'DATE(sr.response_date)';
    //       break;
    //     case 'weekly':
    //       dateFormat = '%Y-W%u';
    //       dateGroupBy = 'YEARWEEK(sr.response_date, 1)';
    //       break;
    //     case 'monthly':
    //       dateFormat = '%Y-%m';
    //       dateGroupBy = 'DATE_FORMAT(sr.response_date, "%Y-%m")';
    //       break;
    //     default:
    //       dateFormat = '%Y-%m-%d';
    //       dateGroupBy = 'DATE(sr.response_date)';
    //   }

    //   // Get trend data grouped by time period and category
    //   const trendData = await this.sequelize.query(`
    //     SELECT
    //       ${dateGroupBy} as period,
    //       DATE_FORMAT(sr.response_date, '${dateFormat}') as periodLabel,
    //       ac.name as categoryName,
    //       ac.display_name as categoryDisplayName,
    //       ac.color_code as categoryColor,
    //       ac.sort_order,
    //       COUNT(*) as responseCount
    //     FROM SurveyResponses sr
    //     JOIN QuestionOptions qo ON sr.selected_option_id = qo.id
    //     JOIN AnswerCategories ac ON qo.category_id = ac.id
    //     WHERE sr.user_id IN (:userIds)
    //       AND sr.response_date BETWEEN :startDate AND :endDate
    //       AND sr.isActive = 1
    //       AND sr.isDeleted = 0
    //     GROUP BY ${dateGroupBy}, ac.id, ac.name, ac.display_name, ac.color_code, ac.sort_order
    //     ORDER BY ${dateGroupBy}, ac.sort_order
    //   `, {
    //     replacements: {
    //       userIds: whereClause.user_id[Op.in],
    //       startDate: whereClause.response_date[Op.between][0],
    //       endDate: whereClause.response_date[Op.between][1]
    //     },
    //     type: QueryTypes.SELECT
    //   });


    //   // Transform data for timeline and chart
    //   const timelineMap = new Map();
    //   const categories = ['VERY_POSITIVE', 'POSITIVE', 'NEUTRAL', 'NEGATIVE', 'VERY_NEGATIVE'];
    //   const categoryColors = {
    //     'VERY_POSITIVE': '#28a745',
    //     'POSITIVE': '#17a2b8',
    //     'NEUTRAL': '#ffc107',
    //     'NEGATIVE': '#fd7e14',
    //     'VERY_NEGATIVE': '#dc3545'
    //   };

    //   // Initialize timeline data structure
    //   trendData.forEach((item: any) => {
    //     if (!timelineMap.has(item.periodLabel)) {
    //       timelineMap.set(item.periodLabel, {
    //         period: item.periodLabel,
    //         date: item.period,
    //         VERY_POSITIVE: 0,
    //         POSITIVE: 0,
    //         NEUTRAL: 0,
    //         NEGATIVE: 0,
    //         VERY_NEGATIVE: 0,
    //         total: 0
    //       });
    //     }

    //     const timelineItem = timelineMap.get(item.periodLabel);
    //     timelineItem[item.categoryName] = parseInt(item.responseCount);
    //     timelineItem.total += parseInt(item.responseCount);
    //   });

    //   const timeline = Array.from(timelineMap.values()).sort((a, b) => a.date.localeCompare(b.date));

    //   // Prepare chart data
    //   const labels = timeline.map(item => item.period);
    //   const datasets = categories.map(category => ({
    //     label: category.replace('_', ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase()),
    //     data: timeline.map(item => item[category]),
    //     backgroundColor: categoryColors[category] + '80', // Add transparency
    //     borderColor: categoryColors[category],
    //     borderWidth: 2,
    //     fill: false
    //   }));

    //   const result = {
    //     timeline,
    //     chartData: {
    //       labels,
    //       datasets
    //     },
    //     summary: {
    //       totalPeriods: timeline.length,
    //       totalResponses: timeline.reduce((sum, item) => sum + item.total, 0),
    //       averageResponsesPerPeriod: timeline.length > 0 ?
    //         Math.round((timeline.reduce((sum, item) => sum + item.total, 0) / timeline.length) * 10) / 10 : 0
    //     }
    //   };

    //   return result;

    // } catch (error) {
    //   console.error('❌ Error in getDashboardTrendAnalysis:', error);
    //   // Return empty structure on error
    //   return {
    //     timeline: [],
    //     chartData: { labels: [], datasets: [] },
    //     summary: { totalPeriods: 0, totalResponses: 0, averageResponsesPerPeriod: 0 }
    //   };
    // }
  }

  private async getDashboardDepartmentAnalytics(whereClause: any, user: any) {
    try {
      // Get accessible user IDs for filtering
      const accessibleUserIds = whereClause.user_id[Op.in];
      const startDate = whereClause.response_date[Op.between][0]
      const endDate = whereClause.response_date[Op.between][1]

      // Build department filter based on user role
      let departmentWhere: any = {
        company_id: user.companyId,
        isDeleted: false
      };

      // Apply role-based filtering for departments
      if (user.role === 'CompanyAdministrator' || user.role === 'CompanyAdmin') {
        // CompanyAdmin and CompanyAdministrator can see all departments
        // No additional filtering needed
      } else if (user.role === 'Supervisor' || user.role === 'CompanyManager' || user.role === 'Employee') {
        // Other roles can only see their own department data
        if (user.department_id) {
          departmentWhere.id = user.department_id;
        } else {
          // If user has no department, return empty results
          return {
            departments: [],
            responseRateChart: { labels: [], datasets: [] },
            satisfactionChart: { labels: [], datasets: [] }
          };
        }
      }

      // Get departments based on role permissions using Sequelize models
      const departments = await this.departmentModel.findAll({
        where: departmentWhere,
        include: [
          {
            model: User,
            as: 'users',
            where: {
              company_id: user.companyId,
              isActive: true,
              isDeleted: false
            },
            required: false,
            include: [
              {
                model: SurveyResponse,
                as: 'surveyResponses',
                where: {
                  user_id: { [Op.in]: accessibleUserIds },
                  isActive: true,
                  isDeleted: false,
                  ...((startDate && endDate) ? {
                    response_date: {
                      [Op.between]: [startDate, endDate]
                    }
                  } : {})
                },
                required: false,
                include: [
                  {
                    model: QuestionOption,
                    as: 'selectedOption',
                    required: false,
                    include: [
                      {
                        model: AnswerCategory,
                        as: 'category',
                        required: false
                      }
                    ]
                  }
                ]
              }
            ]
          }
        ],
        order: [['name', 'ASC']]
      });

      const departmentData = departments.map((dept: any) => {
        const deptPlain = dept.get({ plain: true });
        const users = deptPlain.users || [];

        const totalEmployees = users.length;
        const activeEmployeeIds = new Set<string>();
        const uniqueResponseKeys = new Set<string>();
        const satisfactionByUniqueKey = new Map<string, number>(); // key -> score

        users.forEach((user: any) => {
          activeEmployeeIds.add(user.id);

          if (user.surveyResponses && user.surveyResponses.length > 0) {
            user.surveyResponses.forEach((response: any) => {
              const sessionId = response.survey_session_id;
              const dateKey = response.response_date?.toISOString().slice(0, 10); // 'YYYY-MM-DD'
              const uniqueKey = `${user.id}-${sessionId}-${dateKey}`;

              // If already processed, skip for satisfaction score
              if (satisfactionByUniqueKey.has(uniqueKey)) return;

              // Add to unique responses set (used for responseRate)
              uniqueResponseKeys.add(uniqueKey);

              // Map category to score
              const categoryName = response.selectedOption?.category?.name;
              if (categoryName) {
                let score = 4;
                switch (categoryName) {
                  case 'VERY_POSITIVE': score = 5; break;
                  case 'POSITIVE': score = 4; break;
                  case 'NEUTRAL': score = 3; break;
                  case 'NEGATIVE': score = 2; break;
                  case 'VERY_NEGATIVE': score = 1; break;
                }
                satisfactionByUniqueKey.set(uniqueKey, score);
              }
            });
          }
        });

        // Calculate deduplicated average satisfaction score
        const uniqueScores = Array.from(satisfactionByUniqueKey.values());
        const avgSatisfactionScore = uniqueScores.length > 0
          ? uniqueScores.reduce((sum, s) => sum + s, 0) / uniqueScores.length
          : 0;


        const result = {
          id: deptPlain.id,
          name: deptPlain.name,
          description: deptPlain.description,
          totalEmployees,
          activeEmployees: activeEmployeeIds.size,
          uniqueResponseCount: uniqueResponseKeys.size,
          avgSatisfactionScore: Math.round(avgSatisfactionScore * 10) / 10,
          responseRate: totalEmployees > 0
            ? Math.round((uniqueResponseKeys.size / totalEmployees) * 1000) / 10
            : 0
        };

        console.log(`📊 Department ${deptPlain.name} metrics:`, result);
        return result;
      });


      const departmentNames = departmentData.map(d => d.name);
      const responseRates = departmentData.map(d => d.responseRate);
      const satisfactionScores = departmentData.map(d => d.avgSatisfactionScore);

      // Prepare chart data
      const responseRateChart = {
        labels: departmentNames,
        datasets: [{
          label: 'Response Rate (%)',
          data: responseRates,
          backgroundColor: 'rgba(54, 162, 235, 0.6)',
          borderColor: 'rgba(54, 162, 235, 1)',
          borderWidth: 1
        }]
      };

      const satisfactionChart = {
        labels: departmentNames,
        datasets: [{
          label: 'Average Satisfaction Score',
          data: satisfactionScores,
          backgroundColor: 'rgba(75, 192, 192, 0.6)',
          borderColor: 'rgba(75, 192, 192, 1)',
          borderWidth: 1
        }]
      };

      const result = {
        departments: departmentData,
        responseRateChart,
        satisfactionChart
      };

      console.log("results==>", result)

      return result;

    } catch (error) {
      console.error('❌ Error in getDashboardDepartmentAnalytics:', error);
      // Return empty structure on error
      return {
        departments: [],
        responseRateChart: { labels: [], datasets: [] },
        satisfactionChart: { labels: [], datasets: [] }
      };
    }
  }

  private async getDashboardRecentActivity(whereClause: any) {

    try {
      const limit = 10; // Show last 10 responses

      // Get recent survey responses with user and question details
      const recentResponses = await this.sequelize.query(`
        SELECT
          sr.id,
          sr.response_date,
          sr.created_at,
          u.firstName,
          u.lastName,
          u.email,
          d.name as departmentName,
          q.question_text,
          qo.option_text,
          ac.name as categoryName,
          ac.display_name as categoryDisplayName,
          ac.color_code as categoryColor
        FROM SurveyResponses sr
        JOIN Users u ON sr.user_id = u.id
        LEFT JOIN Departments d ON u.department_id = d.id
        JOIN Questions q ON sr.question_id = q.id
        JOIN QuestionOptions qo ON sr.selected_option_id = qo.id
        JOIN AnswerCategories ac ON qo.category_id = ac.id
        WHERE sr.user_id IN (:userIds)
          AND sr.response_date BETWEEN :startDate AND :endDate
          AND sr.isActive = 1
          AND sr.isDeleted = 0
        ORDER BY sr.response_date DESC, sr.created_at DESC
        LIMIT :limit
      `, {
        replacements: {
          userIds: whereClause.user_id[Op.in],
          startDate: whereClause.response_date[Op.between][0],
          endDate: whereClause.response_date[Op.between][1],
          limit
        },
        type: QueryTypes.SELECT
      });


      // Transform the data
      const responses = recentResponses.map((response: any) => ({
        id: response.id,
        responseDate: response.response_date,
        createdAt: response.created_at,
        user: {
          name: `${response.firstName} ${response.lastName}`,
          email: response.email,
          department: response.departmentName || 'No Department'
        },
        question: {
          text: response.question_text
        },
        answer: {
          text: response.option_text,
          category: {
            name: response.categoryName,
            displayName: response.categoryDisplayName,
            color: response.categoryColor
          }
        }
      }));

      // Get total count for pagination (if needed)
      const totalCount = await this.surveyResponseModel.count({ where: whereClause });

      const result = {
        responses,
        pagination: {
          currentPage: 1,
          totalPages: Math.ceil(totalCount / limit),
          totalItems: totalCount,
          itemsPerPage: limit
        }
      };

      return result;

    } catch (error) {
      console.error('❌ Error in getDashboardRecentActivity:', error);
      // Return empty structure on error
      return {
        responses: [],
        pagination: { currentPage: 1, totalPages: 0, totalItems: 0, itemsPerPage: 10 }
      };
    }
  }

  private async getAvailableFilters(user: any) {

    try {
      // Get departments for the user's company
      const departments = await this.sequelize.query(`
        SELECT
          d.id,
          d.name,
          COUNT(u.id) as employeeCount
        FROM Departments d
        LEFT JOIN Users u ON d.id = u.department_id
          AND u.isActive = 1
          AND u.isDeleted = 0
        WHERE d.company_id = :companyId
          AND d.isDeleted = 0
        GROUP BY d.id, d.name
        ORDER BY d.name
      `, {
        replacements: { companyId: user.companyId },
        type: QueryTypes.SELECT
      });

      // Get users based on role permissions
      let users: any[] = [];
      if (['CompanyAdmin', 'Manager'].includes(user.role)) {
        const userQuery = await this.sequelize.query(`
          SELECT
            u.id,
            CONCAT(u.firstName, ' ', u.lastName) as name,
            u.email,
            d.name as departmentName,
            r.name as roleName
          FROM Users u
          LEFT JOIN Departments d ON u.department_id = d.id
          LEFT JOIN Roles r ON u.roleId = r.id
          WHERE u.company_id = :companyId
            AND u.isActive = 1
            AND u.isDeleted = 0
            AND r.name IN ('Employee', 'Supervisor')
          ORDER BY u.firstName, u.lastName
        `, {
          replacements: { companyId: user.companyId },
          type: QueryTypes.SELECT
        });
        users = userQuery;
      }

      const result = {
        departments: departments.map((dept: any) => ({
          id: dept.id,
          name: dept.name,
          employeeCount: parseInt(dept.employeeCount) || 0
        })),
        dateRanges: [
          { label: 'Last 7 days', value: '7d' },
          { label: 'Last 30 days', value: '30d' },
          { label: 'Last 3 months', value: '3m' },
          { label: 'Last 6 months', value: '6m' },
          { label: 'Last year', value: '1y' },
          { label: 'Custom range', value: 'custom' }
        ],
        users: users.map((u: any) => ({
          id: u.id,
          name: u.name,
          email: u.email,
          department: u.departmentName || 'No Department',
          role: u.roleName
        })),
        periods: [
          { label: 'Daily', value: 'daily' },
          { label: 'Weekly', value: 'weekly' },
          { label: 'Monthly', value: 'monthly' }
        ]
      };


      return result;

    } catch (error) {
      console.error('❌ Error in getAvailableFilters:', error);
      // Return basic structure on error
      return {
        departments: [],
        dateRanges: [
          { label: 'Last 7 days', value: '7d' },
          { label: 'Last 30 days', value: '30d' },
          { label: 'Last 3 months', value: '3m' }
        ],
        users: [],
        periods: [
          { label: 'Weekly', value: 'weekly' }
        ]
      };
    }
  }
}
