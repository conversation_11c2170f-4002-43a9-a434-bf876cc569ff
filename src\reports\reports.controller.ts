import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Param,
  Query,
  Body,
  UseGuards,
  ParseUUIDPipe,
  BadRequestException
} from '@nestjs/common';
import { JwtAuthGuard } from 'src/auth/guards/jwt-auth-guards';
import { ReportsService } from './reports.service';
import { CreateReportDto, UpdateReportDto, ReportQueryDto, GenerateReportDto } from './dto/report.dto';
import { CurrentUser } from 'src/auth/current-user-decorator';
import { CurrentUser as CurrentUserType } from 'src/auth/types/current-user.interface';


@Controller('api/reports')
@UseGuards(JwtAuthGuard)
export class ReportsController {
  constructor(private readonly reportsService: ReportsService) {}

  /**
   * Get unified dashboard with all survey analytics based on user role
   * GET /api/reports/dashboard
   */
  @Get('dashboard')
  async getDashboard(
    @CurrentUser() user: CurrentUserType,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('departmentId') departmentId?: string,
    @Query('period') period: 'daily' | 'weekly' | 'monthly' = 'weekly'
  ) {
    try {
      // Log the user object to verify department_id is included
      console.log('🔍 Dashboard user object:', JSON.stringify(user, null, 2));
      console.log('📍 User department_id:', user.department_id);

      const dateRange = this.validateAndParseDateRange(startDate, endDate);

      return await this.reportsService.getDashboardData(
        user,
        dateRange.startDate,
        dateRange.endDate,
        departmentId,
        period
      );
    } catch (error) {
      throw new BadRequestException(`Failed to get dashboard data: ${error.message}`);
    }
  }

  /**
   * Get comprehensive survey analytics for a company
   * GET /api/reports/company/:companyId/survey-analytics
   */
  @Get('company/:companyId/survey-analytics')
  async getCompanySurveyAnalytics(
    @Param('companyId', ParseUUIDPipe) companyId: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('departmentFilter') departmentFilter?: string
  ) {
    try {
      const dateRange = this.validateAndParseDateRange(startDate, endDate);
      
      return await this.reportsService.getCompanySurveyAnalytics(
        companyId,
        dateRange.startDate,
        dateRange.endDate,
        departmentFilter
      );
    } catch (error) {
      throw new BadRequestException(`Failed to get company analytics: ${error.message}`);
    }
  }

  /**
   * Get survey analytics for a supervisor's team
   * GET /api/reports/supervisor/:supervisorId/team-survey
   */
  @Get('supervisor/:supervisorId/team-survey')
  async getSupervisorTeamAnalytics(
    @Param('supervisorId', ParseUUIDPipe) supervisorId: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string
  ) {
    try {
      const dateRange = this.validateAndParseDateRange(startDate, endDate);
      
      return await this.reportsService.getSupervisorTeamAnalytics(
        supervisorId,
        dateRange.startDate,
        dateRange.endDate
      );
    } catch (error) {
      throw new BadRequestException(`Failed to get supervisor analytics: ${error.message}`);
    }
  }

  /**
   * Get category breakdown for specific questions
   * GET /api/reports/questions/:questionId/category-breakdown
   */
  @Get('questions/:questionId/category-breakdown')
  async getQuestionCategoryBreakdown(
    @Param('questionId', ParseUUIDPipe) questionId: string,
    @Query('companyId', ParseUUIDPipe) companyId?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string
  ) {
    try {
      const dateRange = this.validateAndParseDateRange(startDate, endDate);
      
      return await this.reportsService.getQuestionCategoryBreakdown(
        questionId,
        companyId,
        dateRange.startDate,
        dateRange.endDate
      );
    } catch (error) {
      throw new BadRequestException(`Failed to get question breakdown: ${error.message}`);
    }
  }

  /**
   * Get time series data for trend analysis
   * GET /api/reports/company/:companyId/trend-analysis
   */
  @Get('company/:companyId/trend-analysis')
  async getTrendAnalysis(
    @Param('companyId', ParseUUIDPipe) companyId: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('interval') interval: 'daily' | 'weekly' | 'monthly' = 'weekly'
  ) {
    try {
      const dateRange = this.validateAndParseDateRange(startDate, endDate);
      
      return await this.reportsService.getTrendAnalysis(
        companyId,
        dateRange.startDate,
        dateRange.endDate,
        interval
      );
    } catch (error) {
      throw new BadRequestException(`Failed to get trend analysis: ${error.message}`);
    }
  }

  /**
   * Get department-wise analytics
   * GET /api/reports/company/:companyId/department-analytics
   */
  @Get('company/:companyId/department-analytics')
  async getDepartmentAnalytics(
    @Param('companyId', ParseUUIDPipe) companyId: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string
  ) {
    try {
      const dateRange = this.validateAndParseDateRange(startDate, endDate);
      
      return await this.reportsService.getDepartmentAnalytics(
        companyId,
        dateRange.startDate,
        dateRange.endDate
      );
    } catch (error) {
      throw new BadRequestException(`Failed to get department analytics: ${error.message}`);
    }
  }

  /**
   * Export report data
   * GET /api/reports/export/:companyId
   */
  @Get('export/:companyId')
  async exportReport(
    @Param('companyId', ParseUUIDPipe) companyId: string,
    @Query('format') format: 'json' | 'csv' = 'json',
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string
  ) {
    try {
      const dateRange = this.validateAndParseDateRange(startDate, endDate);
      
      return await this.reportsService.exportReportData(
        companyId,
        format,
        dateRange.startDate,
        dateRange.endDate
      );
    } catch (error) {
      throw new BadRequestException(`Failed to export report: ${error.message}`);
    }
  }

  /**
   * Get all reports with filtering
   * GET /api/reports
   */
  @Get()
  async getReports(@Query() queryDto: ReportQueryDto) {
    try {
      return await this.reportsService.getReports(queryDto);
    } catch (error) {
      throw new BadRequestException(`Failed to get reports: ${error.message}`);
    }
  }

  /**
   * Get a specific report by ID
   * GET /api/reports/:id
   */
  @Get(':id')
  async getReportById(@Param('id', ParseUUIDPipe) id: string) {
    try {
      return await this.reportsService.getReportById(id);
    } catch (error) {
      throw new BadRequestException(`Failed to get report: ${error.message}`);
    }
  }

  /**
   * Create a new report
   * POST /api/reports
   */
  @Post()
  async createReport(
    @Body() createReportDto: CreateReportDto,
    @CurrentUser() user: any
  ) {
    try {
      createReportDto.requested_by = user.userId;
      createReportDto.createdBy = user.userId;
      return await this.reportsService.createReport(createReportDto);
    } catch (error) {
      throw new BadRequestException(`Failed to create report: ${error.message}`);
    }
  }

  /**
   * Generate and store a report
   * POST /api/reports/generate
   */
  @Post('generate')
  async generateReport(
    @Body() generateReportDto: GenerateReportDto,
    @CurrentUser() user: any
  ) {
    try {
      return await this.reportsService.generateAndStoreReport(generateReportDto, user.userId);
    } catch (error) {
      throw new BadRequestException(`Failed to generate report: ${error.message}`);
    }
  }

  /**
   * Update a report
   * PUT /api/reports/:id
   */
  @Put(':id')
  async updateReport(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateReportDto: UpdateReportDto,
    @CurrentUser() user: any
  ) {
    try {
      updateReportDto.updatedBy = user.userId;
      return await this.reportsService.updateReport(id, updateReportDto);
    } catch (error) {
      throw new BadRequestException(`Failed to update report: ${error.message}`);
    }
  }

  /**
   * Delete a report
   * DELETE /api/reports/:id
   */
  @Delete(':id')
  async deleteReport(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentUser() user: any
  ) {
    try {
      return await this.reportsService.deleteReport(id, user.userId);
    } catch (error) {
      throw new BadRequestException(`Failed to delete report: ${error.message}`);
    }
  }

  /**
   * Clean up expired reports
   * POST /api/reports/cleanup
   */
  @Post('cleanup')
  async cleanupExpiredReports() {
    try {
      return await this.reportsService.cleanupExpiredReports();
    } catch (error) {
      throw new BadRequestException(`Failed to cleanup reports: ${error.message}`);
    }
  }

  /**
   * Debug endpoint to get all survey responses without filters
   * GET /api/reports/debug/all-survey-responses
   */
  @Get('debug/all-survey-responses')
  async getAllSurveyResponses() {
    const { QueryTypes } = require('sequelize');
    const sequelize = this.reportsService['sequelize']; // Access private sequelize instance

    const allResponses = await sequelize.query(`
      SELECT
        sr.id,
        sr.user_id,
        sr.selected_option_id,
        sr.response_date,
        sr.isActive,
        sr.isDeleted,
        qo.option_text,
        qo.category_id,
        qo.isDeleted as option_isDeleted,
        ac.name as category_name,
        ac.display_name as category_display_name,
        ac.isDeleted as category_isDeleted
      FROM SurveyResponses sr
      LEFT JOIN QuestionOptions qo ON sr.selected_option_id = qo.id
      LEFT JOIN AnswerCategories ac ON qo.category_id = ac.id
      ORDER BY sr.response_date DESC
    `, {
      type: QueryTypes.SELECT
    });

    return {
      total: allResponses.length,
      data: allResponses
    };
  }

  /**
   * Helper method to validate and parse date range
   */
  private validateAndParseDateRange(startDate?: string, endDate?: string) {
    const now = new Date();
    const defaultStartDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000); // 30 days ago

    let parsedStartDate = defaultStartDate;
    let parsedEndDate = now;

    if (startDate) {
      parsedStartDate = new Date(startDate);
      if (isNaN(parsedStartDate.getTime())) {
        throw new BadRequestException('Invalid start date format');
      }
    }

    if (endDate) {
      parsedEndDate = new Date(endDate);
      if (isNaN(parsedEndDate.getTime())) {
        throw new BadRequestException('Invalid end date format');
      }
    }

    if (parsedStartDate > parsedEndDate) {
      throw new BadRequestException('Start date cannot be after end date');
    }

    return {
      startDate: parsedStartDate,
      endDate: parsedEndDate
    };
  }
}
