#!/usr/bin/env node

/**
 * Sequential Migration Runner
 * Runs migrations in the correct dependency order
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configuration
const MIGRATIONS_PATH = path.join(__dirname, '../src/database/migrations-generated');
const FALLBACK_MIGRATIONS_PATH = path.join(__dirname, '../src/database/migrations');

console.log('🚀 Running Migrations Sequentially...');
console.log('===================================');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function getMigrationsPath() {
  if (fs.existsSync(MIGRATIONS_PATH)) {
    log(`📁 Using generated migrations: ${MIGRATIONS_PATH}`, 'blue');
    return MIGRATIONS_PATH;
  } else if (fs.existsSync(FALLBACK_MIGRATIONS_PATH)) {
    log(`📁 Using fallback migrations: ${FALLBACK_MIGRATIONS_PATH}`, 'yellow');
    return FALLBACK_MIGRATIONS_PATH;
  } else {
    throw new Error('No migrations directory found. Run npm run generate:migrations-from-models first.');
  }
}

function getMigrationFiles(migrationsPath) {
  return fs.readdirSync(migrationsPath)
    .filter(file => file.endsWith('.js'))
    .sort(); // Files are timestamped, so sorting gives correct order
}

function runCommand(command) {
  return new Promise((resolve, reject) => {
    log(`Executing: ${command}`, 'cyan');
    try {
      const output = execSync(command, { 
        stdio: 'pipe',
        encoding: 'utf8',
        cwd: process.cwd()
      });
      if (output) {
        log(output, 'green');
      }
      resolve(output);
    } catch (error) {
      log(`Error: ${error.message}`, 'red');
      if (error.stdout) {
        log(`Stdout: ${error.stdout}`, 'yellow');
      }
      if (error.stderr) {
        log(`Stderr: ${error.stderr}`, 'red');
      }
      reject(error);
    }
  });
}

async function checkDatabaseConnection() {
  try {
    log('🔍 Checking database connection...', 'magenta');
    await runCommand('npx sequelize-cli db:migrate:status');
    log('✅ Database connection successful', 'green');
    return true;
  } catch (error) {
    log('❌ Database connection failed', 'red');
    log('💡 Make sure your database server is running and config is correct', 'yellow');
    return false;
  }
}

async function runMigrationsSequentially() {
  try {
    // Step 1: Check database connection
    const dbConnected = await checkDatabaseConnection();
    if (!dbConnected) {
      throw new Error('Database connection failed');
    }

    // Step 2: Determine migrations path
    const migrationsPath = getMigrationsPath();
    const migrationFiles = getMigrationFiles(migrationsPath);
    
    if (migrationFiles.length === 0) {
      log('⚠️ No migration files found', 'yellow');
      return;
    }

    log(`📋 Found ${migrationFiles.length} migration files:`, 'blue');
    migrationFiles.forEach((file, index) => {
      log(`  ${index + 1}. ${file}`, 'reset');
    });

    // Step 3: Check current migration status
    log('\n📊 Current migration status:', 'magenta');
    try {
      await runCommand(`npx sequelize-cli db:migrate:status --migrations-path "${migrationsPath}"`);
    } catch (error) {
      log('Could not check migration status, continuing...', 'yellow');
    }

    // Step 4: Run migrations
    log('\n⚡ Running pending migrations...', 'magenta');
    await runCommand(`npx sequelize-cli db:migrate --migrations-path "${migrationsPath}"`);
    
    // Step 5: Final status check
    log('\n📊 Final migration status:', 'magenta');
    await runCommand(`npx sequelize-cli db:migrate:status --migrations-path "${migrationsPath}"`);

    log('\n🎉 All migrations completed successfully!', 'green');
    log('✅ Database schema is now up to date', 'green');

  } catch (error) {
    log('\n❌ Migration failed!', 'red');
    log(`Error: ${error.message}`, 'red');
    
    log('\n🔧 Troubleshooting tips:', 'yellow');
    log('1. Check your database connection in src/config/config.js', 'reset');
    log('2. Ensure your database server is running', 'reset');
    log('3. Verify database permissions', 'reset');
    log('4. Check for any foreign key constraint issues', 'reset');
    log('5. Run with --verbose flag for detailed output', 'reset');
    
    process.exit(1);
  }
}

async function rollbackMigrations(steps = 1) {
  try {
    const migrationsPath = getMigrationsPath();
    
    log(`🔄 Rolling back ${steps} migration(s)...`, 'bright');
    
    for (let i = 0; i < steps; i++) {
      log(`Rolling back migration ${i + 1}/${steps}...`, 'yellow');
      await runCommand(`npx sequelize-cli db:migrate:undo --migrations-path "${migrationsPath}"`);
    }

    log('\n✅ Rollback completed successfully!', 'green');
    
    // Show final status
    log('\n📊 Final migration status:', 'magenta');
    await runCommand(`npx sequelize-cli db:migrate:status --migrations-path "${migrationsPath}"`);

  } catch (error) {
    log('\n❌ Rollback failed!', 'red');
    log(`Error: ${error.message}`, 'red');
    process.exit(1);
  }
}

async function showMigrationStatus() {
  try {
    const migrationsPath = getMigrationsPath();
    log('📊 Current migration status:', 'magenta');
    await runCommand(`npx sequelize-cli db:migrate:status --migrations-path "${migrationsPath}"`);
  } catch (error) {
    log('❌ Could not check migration status!', 'red');
    log(`Error: ${error.message}`, 'red');
    process.exit(1);
  }
}

// Parse command line arguments
const args = process.argv.slice(2);
const command = args[0];

switch (command) {
  case 'up':
  case 'migrate':
    runMigrationsSequentially();
    break;
  
  case 'down':
  case 'rollback':
    const steps = parseInt(args[1]) || 1;
    rollbackMigrations(steps);
    break;
  
  case 'status':
    showMigrationStatus();
    break;
  
  case 'help':
  case '--help':
  case '-h':
    log('Sequential Migration Runner - Help', 'bright');
    log('', 'reset');
    log('Usage:', 'cyan');
    log('  node scripts/run-migrations-sequential.js <command> [options]', 'reset');
    log('', 'reset');
    log('Commands:', 'cyan');
    log('  up, migrate     Run all pending migrations sequentially', 'reset');
    log('  down, rollback  Rollback migrations (default: 1 step)', 'reset');
    log('  status          Show current migration status', 'reset');
    log('  help            Show this help message', 'reset');
    log('', 'reset');
    log('Examples:', 'cyan');
    log('  node scripts/run-migrations-sequential.js up', 'reset');
    log('  node scripts/run-migrations-sequential.js down', 'reset');
    log('  node scripts/run-migrations-sequential.js down 3', 'reset');
    log('  node scripts/run-migrations-sequential.js status', 'reset');
    break;
  
  default:
    if (!command) {
      // Default to running migrations
      runMigrationsSequentially();
    } else {
      log('❌ Unknown command. Use "help" for usage information.', 'red');
      process.exit(1);
    }
}
