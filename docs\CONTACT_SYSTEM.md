# 📧 Contact Form System - Domain Restricted API

A secure, domain-restricted contact form system that accepts submissions without authentication but strictly controls access by domain origin.

## 🚀 Features

- ✅ **Domain-Restricted Access** - Only specified domains can submit forms
- ✅ **No Authentication Required** - Public endpoint for form submissions
- ✅ **Spam Detection** - Built-in spam filtering and detection
- ✅ **Rate Limiting** - Prevents abuse with configurable limits
- ✅ **Admin Management** - Full CRUD operations for managing submissions
- ✅ **Status Tracking** - Track submission status (new, read, replied, closed)
- ✅ **IP & User Agent Logging** - Security and analytics tracking
- ✅ **CORS Configuration** - Proper cross-origin resource sharing setup

## 📋 API Endpoints

### **Public Endpoints (Domain Restricted)**

#### **Submit Contact Form**
```http
POST /contact/submit
Content-Type: application/json

{
  "firstName": "John",
  "lastName": "Doe", 
  "email": "<EMAIL>",
  "phone": "+**********",
  "subject": "Inquiry about your services",
  "message": "I would like to know more about your employee survey system."
}
```

**Response:**
```json
{
  "success": true,
  "message": "Thank you for your message! We will get back to you soon.",
  "data": {
    "id": "uuid",
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "subject": "Inquiry about your services",
    "status": "new",
    "createdAt": "2025-01-27T10:00:00Z"
  }
}
```

#### **Health Check**
```http
GET /contact/health
```

### **Admin Endpoints (Authentication Required)**

#### **Get All Submissions**
```http
GET /contact/submissions?page=1&limit=20&status=new&includeSpam=false
Authorization: Bearer JWT_TOKEN
```

#### **Get Submission Stats**
```http
GET /contact/submissions/stats
Authorization: Bearer JWT_TOKEN
```

#### **Get Specific Submission**
```http
GET /contact/submissions/:id
Authorization: Bearer JWT_TOKEN
```

#### **Update Submission Status**
```http
PUT /contact/submissions/:id/status
Authorization: Bearer JWT_TOKEN
Content-Type: application/json

{
  "status": "replied"
}
```

#### **Mark as Spam**
```http
PUT /contact/submissions/:id/spam
Authorization: Bearer JWT_TOKEN
```

#### **Delete Submission**
```http
DELETE /contact/submissions/:id
Authorization: Bearer JWT_TOKEN
```

## 🔧 Configuration

### **Environment Variables**

```bash
# Domain Configuration
CONTACT_ALLOWED_DOMAINS=yourdomain.com,www.yourdomain.com,*.yourdomain.com

# Rate Limiting
CONTACT_RATE_LIMIT_WINDOW_MS=900000  # 15 minutes
CONTACT_RATE_LIMIT_MAX=5             # 5 submissions per window

# Spam Detection
CONTACT_SPAM_DETECTION_ENABLED=true
CONTACT_SPAM_KEYWORD_THRESHOLD=3
CONTACT_SPAM_LINK_THRESHOLD=3

# Notifications
CONTACT_NOTIFICATION_EMAIL=<EMAIL>
CONTACT_WEBHOOK_URL=https://your-webhook-url.com

# Security
CONTACT_ENABLE_HONEYPOT=true
CONTACT_ENABLE_CAPTCHA=false
CONTACT_CAPTCHA_SECRET_KEY=your-captcha-secret
```

### **Domain Configuration by Environment**

#### **Development**
```javascript
allowedDomains: [
  'localhost',
  '127.0.0.1', 
  'localhost:3000',
  'localhost:4200',
  '*.localhost'
]
```

#### **Staging**
```javascript
allowedDomains: [
  'staging.yourdomain.com',
  'test.yourdomain.com'
]
```

#### **Production**
```javascript
allowedDomains: [
  'yourdomain.com',
  'www.yourdomain.com',
  '*.yourdomain.com'
]
```

## 🛡️ Security Features

### **Domain Restriction**
- Validates `Origin`, `Referer`, and `Host` headers
- Supports wildcard subdomains (`*.example.com`)
- Logs all access attempts for monitoring
- Blocks unauthorized domains with detailed error messages

### **Spam Detection**
- Keyword-based filtering
- Link count analysis
- Duplicate submission detection
- IP-based rate limiting
- Automatic spam flagging

### **Rate Limiting**
- Configurable time windows
- Per-IP submission limits
- Prevents form abuse
- Graceful error handling

## 📊 Database Schema

```sql
CREATE TABLE ContactSubmissions (
  id UUID PRIMARY KEY,
  firstName VARCHAR(100) NOT NULL,
  lastName VARCHAR(100) NOT NULL,
  email VARCHAR(255) NOT NULL,
  phone VARCHAR(20),
  subject VARCHAR(255) NOT NULL,
  message TEXT NOT NULL,
  ipAddress VARCHAR(45),
  userAgent VARCHAR(500),
  refererDomain VARCHAR(255),
  status ENUM('new', 'read', 'replied', 'closed') DEFAULT 'new',
  isSpam BOOLEAN DEFAULT false,
  createdAt TIMESTAMP DEFAULT NOW(),
  updatedAt TIMESTAMP DEFAULT NOW(),
  isDeleted BOOLEAN DEFAULT false,
  deletedAt TIMESTAMP NULL
);
```

## 🎨 Frontend Integration

### **HTML Form Example**
```html
<form id="contactForm">
  <input type="text" name="firstName" required>
  <input type="text" name="lastName" required>
  <input type="email" name="email" required>
  <input type="tel" name="phone">
  <input type="text" name="subject" required>
  <textarea name="message" required></textarea>
  <button type="submit">Send Message</button>
</form>
```

### **JavaScript Submission**
```javascript
const form = document.getElementById('contactForm');
form.addEventListener('submit', async (e) => {
  e.preventDefault();
  
  const formData = new FormData(form);
  const data = Object.fromEntries(formData);
  
  try {
    const response = await fetch('/contact/submit', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data)
    });
    
    const result = await response.json();
    
    if (result.success) {
      alert(result.message);
      form.reset();
    } else {
      alert('Error: ' + result.message);
    }
  } catch (error) {
    alert('Network error. Please try again.');
  }
});
```

## 🚀 Setup Instructions

### **1. Run Migration**
```bash
npm run db:migrate
```

### **2. Update App Module**
```typescript
import { ContactModule } from './contact/contact.module';

@Module({
  imports: [
    // ... other modules
    ContactModule
  ]
})
export class AppModule {}
```

### **3. Configure CORS**
```typescript
import { getEnvironmentCorsConfig } from './config/cors.config';

const app = await NestFactory.create(AppModule);
app.enableCors(getEnvironmentCorsConfig());
```

### **4. Set Environment Variables**
```bash
# Copy and customize
cp .env.example .env
```

### **5. Test the Form**
Open `public/contact-form-example.html` in your browser and test submissions.

## 📈 Monitoring & Analytics

### **Submission Statistics**
- Total submissions
- Status breakdown (new, read, replied, closed)
- Spam detection metrics
- Domain-wise analytics

### **Security Monitoring**
- Failed domain access attempts
- Rate limit violations
- Spam detection triggers
- IP-based activity tracking

## 🔧 Customization

### **Adding Custom Validation**
```typescript
// In DTO
@IsCustom()
customField: string;
```

### **Custom Spam Detection**
```typescript
// In service
private async customSpamDetection(data: any): Promise<boolean> {
  // Your custom logic
  return false;
}
```

### **Webhook Notifications**
```typescript
// Add to service
private async sendWebhookNotification(submission: any) {
  // Send to external webhook
}
```

## 🎯 Best Practices

1. **Always validate domains** in production
2. **Monitor spam detection** effectiveness
3. **Set appropriate rate limits** for your use case
4. **Log security events** for analysis
5. **Regularly clean up** old submissions
6. **Test CORS configuration** thoroughly
7. **Use HTTPS** in production

## 🔗 Related Documentation

- [Domain Restriction Guard](./DOMAIN_GUARD.md)
- [CORS Configuration](./CORS_CONFIG.md)
- [Security Best Practices](./SECURITY.md)
